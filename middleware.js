import { NextResponse } from 'next/server'
import { checkAuth, checkAdminAuth } from './lib/utils'
const privatePaths = ['/user']
const authPaths = ['/login', '/register']
const adminPaths = ['/admin',"/api/admin/"]

export async function middleware(request) {
  const { pathname } = request.nextUrl

  // Check admin routes
  if (adminPaths.some((path) => pathname.startsWith(path))) {
    const { isAdmin, needsLogin } = await checkAdminAuth(request.cookies)

    if (needsLogin) {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    if (!isAdmin) {
      return NextResponse.redirect(new URL('/admin-access-denied', request.url))
    }

    return NextResponse.next()
  }

  // Check regular auth routes
  const { isLoggedIn, userInfo } = await checkAuth(request.cookies)

  if (privatePaths.some((path) => pathname.startsWith(path)) && !isLoggedIn) {
    return NextResponse.redirect(new URL('/login', request.url))
  }
  if (authPaths.some((path) => pathname.startsWith(path)) && isLoggedIn) {
    return NextResponse.redirect(new URL('/', request.url))
  }
  return NextResponse.next()
}

export const config = {
  matcher: ['/user/:path*', '/login', '/logout', '/register', '/admin/:path*',"/api/admin/:path*"],
}
