import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST() {
    try {
        const cookieStore = cookies();
        
        // Clear the token cookie
        cookieStore.set('token', '', {
            maxAge: 0,
            httpOnly: true,
            path: '/'
        });
        
        return NextResponse.json({
            success: true,
            message: 'Logged out successfully'
        });
        
    } catch (error) {
        console.error('Error logging out:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to logout'
        }, { status: 500 });
    }
}
