
const CSGOEMPIRE_API_BASE = 'https://csgoempire.com/api/v2';
const BEARER_TOKEN = process.env.CSGOEMPIRE_BEARER_TOKEN || '100bd6d066081c97c45dbd0eefa1668a';

/**
 * Get user transactions from CSGOEmpire API
 * @param {number} page - Page number (default: 1)
 * @param {number} perPage - Items per page (default: 100)
 * @returns {Promise<Object>} API response with transactions
 */
export async function getUserTransactions(page = 1, perPage = 100) {
    try {
        const url = `${CSGOEMPIRE_API_BASE}/user/transactions?page=${page}&per_page=${perPage}`;
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Authorization': `Bearer ${BEARER_TOKEN}`,
                'User-Agent': 'EmpireVN-Client/1.0'
            },
        });

        if (!response.ok) {
            throw new Error(`CSGOEmpire API error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        
        if (!data.success) {
            throw new Error('CSGOEmpire API returned unsuccessful response');
        }

        return {
            success: true,
            data: data.data,
            currentPage: data.current_page,
            totalPages: data.last_page || 1,
            total: data.total || 0
        };

    } catch (error) {
        console.error('Error fetching user transactions:', error);
        return {
            success: false,
            error: error.message,
            data: []
        };
    }
}

/**
 * Get specific transaction details by ID
 * @param {number} transactionId - Transaction ID
 * @returns {Promise<Object>} Transaction details
 */
export async function getTransactionDetails(transactionId) {
    try {
        const url = `${CSGOEMPIRE_API_BASE}/user/transactions/${transactionId}`;
        
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Authorization': `Bearer ${BEARER_TOKEN}`,
                'User-Agent': 'EmpireVN-Client/1.0'
            },
            cache: 'no-store'
        });

        if (!response.ok) {
            throw new Error(`CSGOEmpire API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        
        return {
            success: true,
            data: data
        };

    } catch (error) {
        console.error('Error fetching transaction details:', error);
        return {
            success: false,
            error: error.message,
            data: null
        };
    }
}

/**
 * Filter new positive transactions (incoming coins)
 * @param {Array} transactions - Array of transactions
 * @returns {Array} Filtered positive transactions
 */
export  function filterNewPositiveTransactions(transactions) {
    if (!Array.isArray(transactions)) {
        return [];
    }

    return transactions.filter(transaction => {
        // Check if delta is positive (incoming coins)
        const delta = parseInt(transaction.delta);
        return delta > 0;
    });
    
}


/**
 * Filter new transactions by comparing with previous transactions
 * @param {Array} currentTransactions - Current transactions array
 * @param {Array} previousTransactions - Previous transactions array to compare against
 * @returns {Array} Array of new transactions that weren't in the previous list
 */
export function filterNewTransactions(currentTransactions, previousTransactions = []) {
    if (!Array.isArray(currentTransactions)) {
        return [];
    }

    if (!Array.isArray(previousTransactions) || previousTransactions.length === 0) {
        return currentTransactions;
    }

    // Create a Set of previous transaction IDs for faster lookup
    const previousTransactionIds = new Set(
        previousTransactions.map(transaction => transaction.id)
    );

    // Filter out transactions that already exist in previous list
    const newTransactions = currentTransactions.filter(transaction => {
        return !previousTransactionIds.has(transaction.id);
    });

    return newTransactions;
}

/**
 * Check if transaction is recent (within last 5 minutes)
 * @param {number} timestamp - Transaction timestamp
 * @returns {boolean} True if transaction is recent
 */
export  function isRecentTransaction(timestamp) {
    const now = Date.now();
    const transactionTime = timestamp ; // Convert to milliseconds
    const fiveMinutesAgo = now - (5 * 60 * 1000); // 5 minutes in milliseconds
    
    return transactionTime <= fiveMinutesAgo;
   
}

/**
 * Convert coin amount from API format (coin * 100) to actual coins
 * @param {number} deltaAmount - Delta amount from API
 * @returns {number} Actual coin amount
 */
export  function convertCoinAmount(deltaAmount) {
    return deltaAmount / 100;
}

/**
 * Validate CSGOEmpire API configuration
 * @returns {Object} Validation result
 */
export  function validateApiConfig() {
    const errors = [];
    if (!BEARER_TOKEN) {
        errors.push('CSGOEMPIRE_BEARER_TOKEN environment variable is not set');
    }
    return {
        isValid: errors.length === 0,
        errors
    };
}
