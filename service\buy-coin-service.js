// BuyCoin service functions for server-side operations
import { execute, query } from '../lib/db.js';

/**
 * Create a new buycoin order
 * @param {Object} orderData - Order data
 * @returns {Promise<Object>} Created order result
 */
export async function createBuyCoinOrder(orderData) {
    const { steamId, coin, bankCode, accountNumber, accountName, buyRate } = orderData;

    try {
        // Validate required fields
        if (!steamId || !coin || !bankCode || !accountNumber) {
            throw new Error('Missing required fields');
        }

        // Validate coin amount (minimum 5)
        const coinAmount = parseFloat(coin);
        if (coinAmount < 5) {
            throw new Error('Minimum coin amount is 5');
        }

        // Calculate VND amount (using buy rate 85%)
        const baseRate = 1000; // 1 coin = 1000 VND base rate
        const vndAmount = Math.round(coinAmount * buyRate * baseRate);

        // Generate order ID
        const orderId = generateOrderId();

        // Insert into database
        const query = `
            INSERT INTO buycoin (
                order_id,
                steam_id,
                coin_amount,
                vnd_amount,
                bank_code,
                account_number,
                account_name,
                status,
                created_at,
                updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `;

        const [result] = await execute(query, [
            orderId,
            steamId,
            coinAmount,
            vndAmount,
            bankCode,
            accountNumber,
            accountName || null,
            'created'
        ]);

        return {
            success: true,
            data: {
                id: result.insertId,
                orderId,
                steamId,
                coinAmount,
                vndAmount,
                bankCode,
                accountNumber,
                accountName,
                status: 'created',
                message: 'Đơn hàng bán coin đã được tạo thành công!'
            }
        };

    } catch (error) {
        console.error('Error creating buycoin order:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Get buycoin orders by criteria
 * @param {Object} criteria - Search criteria
 * @returns {Promise<Object>} Orders result
 */
export async function getBuyCoinOrders(criteria = {}) {
    try {
        const { steamId, orderId, status, limit = 50, offset = 0 } = criteria;

        let query = 'SELECT * FROM buycoin WHERE 1=1';
        const params = [];

        if (steamId) {
            query += ' AND steam_id = ?';
            params.push(steamId);
        }

        if (orderId) {
            query += ' AND order_id = ?';
            params.push(orderId);
        }

        if (status) {
            query += ' AND status = ?';
            params.push(status);
        }

        query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
        params.push(parseInt(limit), parseInt(offset));

        const [rows] = await execute(query, params);

        return {
            success: true,
            data: rows,
            total: rows.length
        };

    } catch (error) {
        console.error('Error fetching buycoin orders:', error);
        return {
            success: false,
            error: error.message,
            data: []
        };
    }
}

/**
 * Update buycoin order status
 * @param {string} orderId - Order ID
 * @param {Object} updateData - Update data
 * @returns {Promise<Object>} Update result
 */
export async function updateBuyCoinOrder(orderId, updateData) {
    try {
        const { status, adminNote, processedBy } = updateData;

        let updateQuery = 'UPDATE buycoin SET updated_at = NOW()';
        const params = [];

        if (status) {
            updateQuery += ', status = ?';
            params.push(status);
        }

        if (adminNote) {
            updateQuery += ', admin_note = ?';
            params.push(adminNote);
        }

        if (processedBy) {
            updateQuery += ', processed_by = ?, processed_at = NOW()';
            params.push(processedBy);
        }

        updateQuery += ' WHERE order_id = ?';
        params.push(orderId);

        const [result] = await execute(updateQuery, params);

        if (result.affectedRows === 0) {
            throw new Error('Order not found');
        }

        return {
            success: true,
            message: 'Order updated successfully'
        };

    } catch (error) {
        console.error('Error updating buycoin order:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Get buycoin order by ID
 * @param {string} orderId - Order ID
 * @returns {Promise<Object>} Order result
 */
export async function getBuyCoinOrderById(orderId) {
    try {
        const query = 'SELECT * FROM buycoin WHERE order_id = ?';
        const [rows] = await execute(query, [orderId]);

        if (rows.length === 0) {
            return {
                success: false,
                error: 'Order not found',
                data: null
            };
        }

        return {
            success: true,
            data: rows[0]
        };

    } catch (error) {
        console.error('Error fetching buycoin order:', error);
        return {
            success: false,
            error: error.message,
            data: null
        };
    }
}


export async function getExpiredBuyCoinOrder() {
    const [expiredOrders] = await execute(`
                SELECT id, order_id, steam_id, coin_amount, vnd_amount, created_at
                FROM buycoin
                WHERE status = 'created'
                AND created_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)
                ORDER BY created_at ASC
            `);

    return expiredOrders;

}


export async function updateFailedBuyCoinOrder(id) {
    const [result] = await execute(`
                    UPDATE buycoin
                    SET status = 'failed',
                        admin_note = 'Order expired after 5 minutes',
                        processed_by = 'system_worker',
                        processed_at = NOW(),
                        updated_at = NOW()
                    WHERE id = ? AND status = 'created'
                `, [id]);
    return result.affectedRows > 0;
}


export async function getExpiredOrdersStats() {
    try {
        const [stats] = await db.execute(`
                SELECT 
                    COUNT(*) as total_expired,
                    SUM(vnd_amount) as total_vnd_lost,
                    SUM(coin_amount) as total_coins_lost,
                    DATE(processed_at) as date
                FROM buycoin 
                WHERE status = 'failed' 
                AND admin_note LIKE '%expired%'
                AND processed_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY DATE(processed_at)
                ORDER BY date DESC
            `);
        
         return {
            data: data,
            err: null
         }
    } catch (error) {
       return {
            data: null,
            err: error
         }
    }
}

/**
 * Generate unique order ID
 * @returns {string} Generated order ID
 */
function generateOrderId() {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `LS${timestamp}${random}`;
}

/**
 * Calculate buy rate and VND amount
 * @param {number} coinAmount - Coin amount
 * @returns {Object} Calculation result
 */
export function calculateBuyAmount(coinAmount, buyRate) {
    const baseRate = 1000; // 1 coin = 1000 VND base rate
    const vndAmount = Math.round(coinAmount * buyRate * baseRate);

    return {
        coinAmount,
        buyRate,
        baseRate,
        vndAmount,
        displayRate: `${(buyRate)}%`
    };
}

/**
 * Validate buycoin order data
 * @param {Object} orderData - Order data to validate
 * @returns {Object} Validation result
 */
export function validateBuyCoinOrder(orderData) {
    const errors = {};
    const { steamId, coin, bankCode, accountNumber } = orderData;

    // Validate Steam ID
    if (!steamId || !/^\d{17}$/.test(steamId)) {
        errors.steamId = 'Steam ID phải là chuỗi 17 chữ số';
    }

    // Validate coin amount
    if (!coin) {
        errors.coin = 'Số coin là bắt buộc';
    } else {
        const coinAmount = parseFloat(coin);
        if (isNaN(coinAmount) || coinAmount < 5) {
            errors.coin = 'Số coin tối thiểu là 5.00';
        }
        if (!/^\d+(\.\d{1,2})?$/.test(coin)) {
            errors.coin = 'Coin phải là số thực với tối đa 2 chữ số thập phân';
        }
    }

    // Validate bank code
    if (!bankCode) {
        errors.bankCode = 'Vui lòng chọn ngân hàng';
    }

    // Validate account number
    if (!accountNumber) {
        errors.accountNumber = 'Số tài khoản là bắt buộc';
    } else if (!/^\d{6,20}$/.test(accountNumber)) {
        errors.accountNumber = 'Số tài khoản phải từ 6-20 chữ số';
    }

    return {
        isValid: Object.keys(errors).length === 0,
        errors
    };
}
