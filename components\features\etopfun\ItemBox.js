import { Badge } from "@/components/ui/badge"

export default function ItemBox({ imageUrl, imageBottomShow, price, status }) {

  return (
    <div className="w-24 h-16 relative">
      <img src={imageUrl} width={256} height={170}
      />
      <div className="absolute inset-x-0 bottom-0 bg-black/60 text-center text-xs">
        <span
          style={{ color: imageBottomShow.color }}
          className="block w-full">
          {imageBottomShow.name}
        </span>
      </div>
      <Badge variant="secondary" className="absolute top-0.5 right-0.5 h-3 leading-3 text-xs bg-white text-black opacity-80 px-1">
        {price}
      </Badge>
      <span className="absolute top-0.5 left-0.5 text-[10px] bg-black leading-3 px-0.5">
        {status.new === 1 && <span className="text-yellow-500">N </span>}
        {status.redlock === 1 && <span className="text-red-500">L</span>}
      </span>
    </div>
  )
}

