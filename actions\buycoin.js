"use server";
import { buyCoinSchema } from "@/validations/buycoin.schema";
import { cookies } from 'next/headers';
import { settingCookies } from "@/lib/utils";
import { fetchHome } from "@/lib/http";
import { createBuyCoinOrder, getBuyCoinOrderById } from "@/service/buy-coin-service";
import { fetchBanks, getNameAccount, lookupBankAccount } from "@/service/bank-service";
import { getCacheKey } from "@/lib/bank-lookup-security";
import { getCache } from "@/lib/redis";

export async function buyCoinAction(prevState, data) {
    if (!(data instanceof FormData)) {
        return {
            success: false,
            errorServer: true,
            message: "Dữ liệu không hợp lệ"
        };
    }

    const parseResult = buyCoinSchema.safeParse(Object.fromEntries(data.entries()));

    if (!parseResult.success) {
        return {
            errors: parseResult.error.flatten().fieldErrors,
        };
    }

    const { steamId, coin, bankCode, bin, accountNumber } = parseResult.data;
    const home = await fetchHome()
    if (!home) {
        return {
            errorServer: true,
            message: "Lỗi hệ thống. Vui lòng thử lại sau."
        };
    }
    const { coinBuyRate } = home


    try {
        let accountName = ""
        const cacheKey = getCacheKey(bin, accountNumber);
        const cachedResult = await getCache(cacheKey);
        if (cachedResult) {
            if (!cachedResult.success) {
                return {
                    errorServer: true,
                    message:  "Có lỗi xảy ra khi tra cứu tên tài khoản"
                };
            }
            accountName = cachedResult.accountName;
        }
        else {
            const accountLookup = await getNameAccount(accountNumber, bin);
            console.log(accountLookup);
            if (!accountLookup.success) {
                return {
                    errorServer: true,
                    message: accountLookup.error || "Có lỗi xảy ra khi tra cứu tên tài khoản"
                };
            }
            accountName = accountLookup?.creditorInfo?.name || "";
        }



        // Create buycoin order using server-side service
        const result = await createBuyCoinOrder({
            steamId,
            coin,
            bankCode,
            accountNumber,
            accountName,
            buyRate: coinBuyRate
        });

        if (!result.success) {
            return {
                errorServer: true,
                message: result.error || "Có lỗi xảy ra khi tạo đơn hàng"
            };
        }

        // Save steamId to cookies for future use
        cookies().set('steamId', steamId, settingCookies);

        return {
            success: true,
            data: result.data,
            message: result.data?.message || "Đơn hàng bán coin đã được tạo thành công!"
        };

    } catch (error) {
        console.error('Error creating buycoin order:', error);
        return {
            errorServer: true,
            message: "Lỗi hệ thống. Vui lòng thử lại sau."
        };
    }
}

// Server action để tra cứu tên tài khoản ngân hàng
export async function lookupBankAccountAction(bankCode, accountNumber) {
    try {
        const result = await lookupBankAccount(bankCode, accountNumber);
        return result;
    } catch (error) {
        console.error('Error in lookupBankAccountAction:', error);
        return {
            success: false,
            accountName: "",
            error: error.message
        };
    }
}

// Server action để lấy danh sách ngân hàng
export async function getBanksAction() {
    try {
        const banks = await fetchBanks();
        return {
            success: true,
            data: banks
        };
    } catch (error) {
        console.error('Error in getBanksAction:', error);
        return {
            success: false,
            data: [],
            error: error.message
        };
    }
}

// Server action để kiểm tra trạng thái đơn hàng bán coin
export async function checkBuyCoinOrderStatus(orderId) {
    try {
        const result = await getBuyCoinOrderById(orderId);
        return result;
    } catch (error) {
        console.error('Error in checkBuyCoinOrderStatus:', error);
        return {
            success: false,
            error: error.message,
            data: null
        };
    }
}
