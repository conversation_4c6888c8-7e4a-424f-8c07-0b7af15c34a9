
import { User, <PERSON><PERSON><PERSON>, <PERSON>, CreditC<PERSON> } from 'lucide-react';
import Link from 'next/link';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, } from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Separator } from '@radix-ui/react-dropdown-menu';
import { SheetClose } from "@/components/ui/sheet";
import LogoutButton from '@/components/features/auth/LogoutButton';
export function UserMenuDesktop({ user }) {
    const userMenu = [
        { name: "<PERSON><PERSON><PERSON> kho<PERSON>n", href: "/user/info", icon: User },
        { name: "Đơn hàng", href: "/user/orders", icon: CreditCard },
        { name: "Thông báo", href: "/user/notifications", icon: Bell, },
        { name: "<PERSON>à<PERSON> đặt", href: "/user/settings", icon: Settings },

    ]
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <div className="relative group">
                    {/* Glow effect for light mode */}
                    <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 via-purple-500/20 to-indigo-500/20 rounded-full blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                    {/* Ring border for better visibility in light mode */}
                    <Avatar className="cursor-pointer relative ring-2 ring-border/50 hover:ring-primary/50 transition-all duration-300 shadow-md hover:shadow-lg">
                        <AvatarImage src={user.avatar} alt={user.username} />
                        <AvatarFallback className="bg-gradient-to-br from-primary/10 to-purple-500/10 text-foreground font-semibold border border-border/50">
                            {user.username.charAt(0)}
                        </AvatarFallback>
                    </Avatar>
                </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
                align="end"
                className="w-56 bg-background/95 backdrop-blur-md border border-border/50 shadow-xl"
            >
                <div className="px-3 py-2 border-b border-border/50">
                    <p className="text-sm font-medium text-foreground">{user.username}</p>
                    <p className="text-xs text-muted-foreground">{user?.steamId || user?.email}</p>
                </div>

                {userMenu.map((link) => (
                    <DropdownMenuItem key={link.name} className="hover:bg-accent/50 focus:bg-accent/50">
                        <Link className="flex items-center w-full text-foreground hover:text-primary transition-colors" href={link.href}>
                            <link.icon className="mr-3 h-4 w-4 text-muted-foreground" />
                            <span>{link.name}</span>
                        </Link>
                    </DropdownMenuItem>
                ))}

                <div className="border-t border-border/50 mt-1">
                    <DropdownMenuItem className="hover:bg-destructive/10 focus:bg-destructive/10">
                        <LogoutButton />
                    </DropdownMenuItem>
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

export function UsermenuMobile({ user }) {
    const userMenu = [
        { name: "Tài khoản", href: "/user/info", icon: User },
        { name: "Đơn hàng", href: "/user/orders", icon: CreditCard },
        { name: "Thông báo", href: "/user/notifications", icon: Bell },
        { name: "Cài đặt", href: "/user/settings", icon: Settings },
    ]

    return (
        <div className="border-l border-border/50 mt-4 pt-4 px-5">
            <div className="flex items-center">
                <Avatar className="ring-2 ring-border/30">
                    <AvatarImage src={user.avatar} alt={user.username} />
                    <AvatarFallback className="bg-gradient-to-br from-primary/10 to-purple-500/10 text-foreground font-semibold">
                        {user.username.charAt(0)}
                    </AvatarFallback>
                </Avatar>
                <div className="ml-3">
                    <div className="text-base font-medium text-foreground">{user.username}</div>
                    <div className="text-sm text-muted-foreground">{user?.steamId || user?.email}</div>
                </div>
            </div>
            <div className="mt-3 space-y-1">
                {userMenu.map(link => (
                    <SheetClose key={link.href} asChild>
                        <Link href={link.href}>
                            <Button variant="ghost" className="w-full justify-start text-foreground hover:text-primary hover:bg-accent/50">
                                <link.icon className="mr-2 h-4 w-4 text-muted-foreground" />
                                <span>{link.name}</span>
                            </Button>
                        </Link>
                    </SheetClose>
                ))}
                <Separator className="my-4 bg-border/50" />
                <SheetClose asChild>
                    <LogoutButton />
                </SheetClose>
            </div>
        </div>
    )
}