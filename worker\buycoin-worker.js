import 'server-only';
import { getExpiredBuyCoinOrder, getExpiredOrdersStats, updateFailedBuyCoinOrder } from '@/service/buy-coin-service.js';

/**
 * BuyCoin Order Worker
 * Monitors buycoin orders and marks expired ones as failed
 */

class BuyCoinOrderWorker {
    constructor() {
        this.isRunning = false;
        this.timeoutId = null;
        this.intervalMs = 60000; // 1 minute
        this.lastCheckTime = null;
        this.errorCount = 0;
        this.maxErrors = 5;
        this.stats = {
            totalChecks: 0,
            ordersProcessed: 0,
            ordersExpired: 0,
            errors: 0,
            startTime: null
        };
    }

    /**
     * Start the worker
     * @returns {Promise<Object>} Start result
     */
    async start() {
        try {
            if (this.isRunning) {
                return {
                    success: false,
                    error: 'BuyCoin Order Worker is already running'
                };
            }

            this.isRunning = true;
            this.errorCount = 0;
            this.stats.startTime = new Date();
            this.lastCheckTime = Date.now();

            // Start the recursive timeout loop
            this.scheduleNextExecution();

            return {
                success: true,
                message: 'BuyCoin Order Worker started successfully',
                intervalMs: this.intervalMs
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Stop the worker
     * @returns {Promise<Object>} Stop result
     */
    async stop() {
        try {
            if (!this.isRunning) {
                return {
                    success: false,
                    error: 'BuyCoin Order Worker is not running'
                };
            }

            this.isRunning = false;

            if (this.timeoutId) {
                clearTimeout(this.timeoutId);
                this.timeoutId = null;
            }

            return {
                success: true,
                message: 'BuyCoin Order Worker stopped successfully',
                stats: this.stats
            };

        } catch (error) {
            console.error('Error stopping BuyCoin Order Worker:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get worker status
     * @returns {Object} Worker status
     */
    getStatus() {
        const runtime = this.stats.startTime ?
            Math.round((Date.now() - this.stats.startTime.getTime()) / 1000) : 0;

        return {
            isRunning: this.isRunning,
            intervalMs: this.intervalMs,
            lastCheckTime: this.lastCheckTime,
            errorCount: this.errorCount,
            maxErrors: this.maxErrors,
            runtime,
            stats: this.stats
        };
    }

    /**
     * Schedule the next execution using setTimeout
     */
    scheduleNextExecution() {
        if (!this.isRunning) return;

        this.timeoutId = setTimeout(async () => {
            await this.checkExpiredOrders();
            // Schedule next execution if still running
            if (this.isRunning) {
                this.scheduleNextExecution();
            }
        }, this.intervalMs);
    }

    /**
     * Main worker function - check for expired orders
     */
    async checkExpiredOrders() {
        try {
            this.stats.totalChecks++;
            this.lastCheckTime = Date.now();


            // Find orders that are older than 5 minutes and still in 'created' status
            const expiredOrders = await getExpiredBuyCoinOrder();
            if (expiredOrders.length === 0) {
                return;
            }
            for (const order of expiredOrders) {
                await this.processExpiredOrder(order);
            }
            this.errorCount = 0;

        } catch (error) {
            this.errorCount++;
            this.stats.errors++;

            // Stop worker if too many errors
            if (this.errorCount >= this.maxErrors) {
                console.error(`Too many errors (${this.errorCount}), stopping worker`);
                await this.stop();
                return; // Don't schedule next execution
            }
        }
    }

    /**
     * Process a single expired order
     * @param {Object} order - Order data
     */
    async processExpiredOrder(order) {
        try {
            const result = await updateFailedBuyCoinOrder(order.id);
            if (result) {
                this.stats.ordersExpired++;
            }
            this.stats.ordersProcessed++;

        } catch (error) {
            throw error;
        }
    }

    /**
     * Get expired orders statistics
     * @returns {Promise<Object>} Statistics
     */
    async fetchExpiredOrdersStats() {

        const { data, err } = await getExpiredOrdersStats();

        if (err != null) {
            return {
                success: false,
                error: err.message
            };
        }
        return {
            success: true,
            data: data
        }


    }
}
const globalForWorker = globalThis;
if (!globalForWorker.buyCoinOrderWorker) {
    globalForWorker.buyCoinOrderWorker = new BuyCoinOrderWorker();
}

// Create singleton instance
const buyCoinOrderWorker = globalForWorker.buyCoinOrderWorker

export default buyCoinOrderWorker;
