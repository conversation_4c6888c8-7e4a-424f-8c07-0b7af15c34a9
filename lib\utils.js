
import { clsx } from "clsx";
import { twMerge } from "tailwind-merge"
import { createTwc } from "react-twc";
import { getPayloadIfTokenValidatedEdge } from "@/lib/jwt";
import openid from "openid"
export function cn(...inputs) {
  return twMerge(clsx(inputs));
}
export const twx = createTwc({ compose: cn });

export async function checkAuth(cookies) {
  try {
    const jwtToken = cookies.get('token')?.value
    if (!jwtToken) return {
      isLoggedIn: false,
      userInfo: null
    }
    const payload = await getPayloadIfTokenValidatedEdge(jwtToken)
    if (!payload) {
      return {
        isLoggedIn: false,
        userInfo: null
      }
    }
    return {
      isLoggedIn: true,
      userInfo: payload.payload
    };
  } catch (error) {

    return {
      isLoggedIn: false,
      userInfo: null
    }
  }
}

export const settingCookies = {
  maxAge: 60 * 60 * 24 * 14, // 1 week
  httpOnly: true,
}

export function formatStatus(status){
  const obj ={
    PENDING: "Đang chờ",
    FAILED: "Đã huỷ",
    SUCCESS: "Thành công",
    PROCESSING:"Đang xử lý"
  }
  return obj[status]
}

export const relyingParty = new openid.RelyingParty(
  process.env.RETURN_URL, // URL callback của bạn
  "http://localhost:3000", // Realm (dùng chung với callback URL)
  true, // xác thực theo OpenID 2.0
  false, // không cần chứng nhận yêu cầu đăng nhập
  [] // thêm trường thông tin, nếu cần
);

// Admin credentials
const ADMIN_STEAM_ID = '76561199756464868';
const ADMIN_EMAIL = '<EMAIL>';

export async function checkAdminAuth(cookies) {
  try {
    const { isLoggedIn, userInfo } = await checkAuth(cookies);

    if (!isLoggedIn || !userInfo) {
      return {
        isAdmin: false,
        isLoggedIn: false,
        userInfo: null,
        needsLogin: true
      };
    }

    // Check if user has admin Steam ID (check multiple possible field names)
    const steamId = userInfo.steamId || userInfo.steam_id || userInfo.steamid || userInfo.id;
    const email = userInfo.email || userInfo.emailAddress;

    // Check if user is admin by Steam ID or email
    const isAdminBySteamId = steamId === ADMIN_STEAM_ID;
    const isAdminByEmail = email === ADMIN_EMAIL;
    const isAdmin = isAdminBySteamId || isAdminByEmail;

    return {
      isAdmin,
      isLoggedIn: true,
      userInfo,
      needsLogin: false,
      steamId, // for debugging
      email, // for debugging
      adminType: isAdminBySteamId ? 'steam' : isAdminByEmail ? 'email' : null
    };
  } catch (error) {
    return {
      isAdmin: false,
      isLoggedIn: false,
      userInfo: null,
      needsLogin: true
    };
  }
}