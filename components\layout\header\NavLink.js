
"use client";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Home, Gamepad2 } from "lucide-react";

const LINKS = [
    { name: "Trang chủ", href: "/", icon: Home },
    { name: "<PERSON><PERSON><PERSON><PERSON>", href: "/etopfun", icon: Gamepad2 },
];

export function NavLinkDesktop() {
    const pathname = usePathname();

    return (
        <>
            {LINKS.map(link => {
                const IconComponent = link.icon;
                const isActive = pathname === link.href;

                return (
                    <Link
                        key={link.name}
                        href={link.href}
                        className={`
                            px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 flex items-center space-x-2
                            ${isActive
                                ? 'bg-blue-600 text-white'
                                : 'text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-white dark:hover:bg-slate-700'
                            }
                        `}
                    >
                        <IconComponent className="h-4 w-4" />
                        <span>{link.name}</span>
                    </Link>
                );
            })}
        </>
    );
}

export function NavLinkMobile() {
    const pathname = usePathname();

    return (
        <ul className="flex flex-col space-y-1">
            {LINKS.map(link => {
                const IconComponent = link.icon;
                const isActive = pathname === link.href;

                return (
                    <li key={link.name}>
                        <Link
                            href={link.href}
                            className={`
                                flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300
                                ${isActive
                                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 border-l-4 border-blue-600'
                                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-slate-700/50 hover:text-blue-600 dark:hover:text-blue-400'
                                }
                            `}
                        >
                            <IconComponent className="h-5 w-5" />
                            <span>{link.name}</span>
                        </Link>
                    </li>
                );
            })}
        </ul>
    );
}