'use server'
'server-only'
const baseUrl = process.env.API_SERVER;
const baseUrlEtop = process.env.API_SERVER_ETOP
import { cookies } from 'next/headers'
import { headers } from 'next/headers';
async function handleFetch(url, options = {}) {
    const cookieStore = cookies()
    const headersList = headers()
    const token = cookieStore.get('token')
    const realIP = headersList.get('x-real-ip')
    try {
        const authHeader = new Headers(options?.headers)
        authHeader.append("x-forwarded-for", realIP)
        if (token) {
            authHeader.append("Authorization", `Bearer ${token.value}`)
        }
        options.headers = authHeader
        const response = await fetch(url, options);
        if (!response.ok) {
            const error = await response.json();
            console.error(`Error: ${error.message || 'An error occurred'}`);
            return { error: error.message || 'An error occurred' };
        }
        try {
            let data = await response.json();
            return data
        } catch (error) {
            return null
        }
    } catch (error) {
        console.error(`Network Error: ${error.message}`);
        return null
    }
}

async function handleFetchEtop(url, options = {}) {
    try {
        const headersList = headers()
        const realIP = headersList.get('x-real-ip')
        const authHeader = new Headers(options?.headers)
        authHeader.append("x-forwarded-for", realIP)
        authHeader.append("Authorization", `abc1234`)
        options.headers = authHeader
        const response = await fetch(url, options);
        if (!response.ok) {
            const error = await response.json();
            console.error(`Error: ${error.message || 'An error occurred'}`);
            return { error: error.message || 'An error occurred' };
        }
        let { data, code, message } = await response.json()
      
        if (code == 20001) return data
        else throw new Error(message)
    } catch (error) {
        console.error(`Network Error: ${error.message}`);
        return null
    }

}

export async function loginUser(user) {
    const url = `${baseUrl}auth/login`;
    const options = {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(user),
        cache: "no-cache",
    };
    return handleFetch(url, options);
}

export async function fetchOrder(payload) {
    const url = `${baseUrl}order`;
    const options = {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
    };
    return handleFetch(url, options);
}

export async function fetchOrderStatus(orderId) {
    const url = `${baseUrl}order/status?id=${orderId}`;
    return handleFetch(url);
}

export async function fetchHome() {
    const url = `${baseUrl}home`;
    return handleFetch(url);
}

export async function fetchLevelUser() {
    const url = `${baseUrl}users/level`;
    return handleFetch(url);
}
export async function sendLoginSteam(params) {
    return handleFetch(`${baseUrl}auth/steam/callback?${params}`)
}

export async function sendLoginGoogle(obj) {
    const options = {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(obj),
    };
    return handleFetch(`${baseUrl}auth/google`, options)
}
export async function fetchUserInfo() {
    return handleFetch(`${baseUrl}users/info`)
}

export async function fetchUserOrder(page) {
    return handleFetch(`${baseUrl}my-orders?page=${page}`)
}

export async function fetchAllOrder(page) {
    const body = {
        pageRequest: {
            page: page,
            size: 10,
            sorts: [
                {
                    direction: "ASC",
                    field: "createdAt"
                }
            ]
        }
    }
    return handleFetch(`${baseUrl}order/list`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(body)
    })
}

export async function fetchEtopfunItems() {
    return handleFetchEtop(`${baseUrlEtop}item/`)
}

export async function fetchOrderEtop(payload) {
    const url = `${baseUrl}order/etop`;
    const options = {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-API_KEY":'bd0eefa1668azoqdwtDIWhy7EKJTL33d3421rdsa'
        },
        body: JSON.stringify(payload),
    };
    return handleFetch(url, options);
}

export async function fetchUserEtopfun(steamId) {
    return handleFetchEtop(`${baseUrlEtop}users?steamid=${steamId}`)
}

export async function fetchPreTip(data) {
    const options = {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
    };
    return handleFetchEtop(`${baseUrlEtop}item/presend`, options)
}

export async function fetchBankName(bankBin, accountNumber) {
    const url = `${baseUrl}bank?codeBank=${bankBin}&accountName=${accountNumber}`;
    return handleFetch(url);
}