"use client";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Headphones, Facebook, Phone, MessageCircle, Clock, CheckCircle2 } from "lucide-react";

export default function SupportTab({ facebook }) {
  return (
    <Card className="border-0 shadow-xl bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm">
      <CardHeader className="pb-3 md:pb-4 p-4 md:p-6">
        <CardTitle className="flex items-center space-x-2 md:space-x-3 text-xl md:text-2xl bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
          <Headphones className="h-6 w-6 md:h-7 md:w-7 text-green-600" />
          <span>Hỗ Trợ Khách Hàng</span>
        </CardTitle>
        <p className="text-sm md:text-base text-gray-600 dark:text-gray-400">
          Chúng tôi luôn sẵn sàng hỗ trợ bạn 24/7
        </p>
      </CardHeader>

      <CardContent className="space-y-4 md:space-y-6 p-4 md:p-6 pt-0">
        {/* Contact Methods */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
          {/* Facebook Support */}
          <a
            href={facebook?.value || "https://www.facebook.com/empirevn.store"}
            target="_blank"
            rel="noopener noreferrer"
          >
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-4 md:p-6 rounded-xl border border-blue-200 dark:border-blue-800 hover:shadow-lg transition-shadow duration-200">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                  <Facebook className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 dark:text-gray-200">Facebook</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Fanpage/Messenger</p>
                  <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">Phản hồi nhanh nhất</p>
                </div>
              </div>
            </div>
          </a>

          {/* Phone Support */}
          <a
            href="tel:0948153652"
          >
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-4 md:p-6 rounded-xl border border-green-200 dark:border-green-800 hover:shadow-lg transition-shadow duration-200">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                  <Phone className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 dark:text-gray-200">Hotline</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">0948.153.652</p>
                  <p className="text-xs text-green-600 dark:text-green-400 mt-1">8:00 - 22:00</p>
                </div>
              </div>
            </div>
          </a>
        </div>

        {/* Support Info */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-4 md:p-6 rounded-xl border border-purple-200 dark:border-purple-800">
          <div className="flex items-start space-x-4">
            <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0">
              <MessageCircle className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-800 dark:text-gray-200">Thông Tin Hỗ Trợ</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-blue-500" />
                  <span className="text-gray-600 dark:text-gray-400">Thời gian phản hồi: 5-15 phút</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle2 className="h-4 w-4 text-green-500" />
                  <span className="text-gray-600 dark:text-gray-400">Hỗ trợ 24/7 qua Facebook</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-orange-500" />
                  <span className="text-gray-600 dark:text-gray-400">Hotline: 8:00 - 22:00</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MessageCircle className="h-4 w-4 text-purple-500" />
                  <span className="text-gray-600 dark:text-gray-400">Tư vấn miễn phí</span>
                </div>
              </div>

              <div className="mt-4 p-3 bg-white/70 dark:bg-slate-800/70 rounded-lg">
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  <strong>Các vấn đề chúng tôi hỗ trợ:</strong>
                </p>
                <ul className="text-xs text-gray-600 dark:text-gray-400 mt-2 space-y-1">
                  <li>• Hướng dẫn mua/bán coin</li>
                  <li>• Kiểm tra trạng thái giao dịch</li>
                  <li>• Giải quyết vấn đề thanh toán</li>
                  <li>• Tư vấn tỷ giá và khuyến mãi</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
