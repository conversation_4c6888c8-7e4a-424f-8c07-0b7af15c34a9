"use client";
import { useState, useEffect } from "react";

export function useCurrencyExchange(rate) {
  const [coin, setCoin] = useState("");
  const [vnd, setVnd] = useState("");
  const [lastChanged, setLastChanged] = useState(null);

  // Calculate VND when coin changes
  useEffect(() => {
    if (coin && lastChanged === "coin") {
      let calculatedVnd = 0;
      const coin100 = Math.round(coin * 100);
      if (coin100 < 1000) {
        calculatedVnd = Math.round(coin100 * 10 * rate);
      } else {
        calculatedVnd = Math.round(coin100 * rate / 100.0) * 1000;
      }
      setVnd(calculatedVnd);
    }
  }, [coin, lastChanged, rate]);

  // Calculate coin when VND changes
  useEffect(() => {
    if (vnd && lastChanged === "vnd") {
      const calculatedCoin = Math.floor((Number.parseFloat(vnd) / (rate * 1000)) * 100) / 100;
      setCoin(calculatedCoin.toString());
    }
  }, [vnd, lastChanged, rate]);

  const handleCoinChange = (e) => {
    setCoin(e.target.value);
    setLastChanged("coin");
  };

  const handleVndChange = (e) => {
    setVnd(e.target.value);
    setLastChanged("vnd");
  };

  const resetValues = () => {
    setCoin("");
    setVnd("");
    setLastChanged(null);
  };

  return {
    coin,
    vnd,
    handleCoinChange,
    handleVndChange,
    resetValues
  };
}
