import Link from 'next/link';
import { Facebook, Send,Mail } from 'lucide-react';
export default function Footer() {
    return (
        <div className="inset-x-0 bottom-0 border-t border-slate-700 pt-4 px-5 block justify-center w-full">
            <div className='p-1'>
                <div className="flex justify-center items-center w-auto space-x-4">
                    <Link href="https://www.facebook.com/hoangminh9119/" target="_blank" rel="noopener noreferrer">
                        <Facebook className="h-6 w-6 text-slate-400 hover:text-blue-500" />
                    </Link>
                    <Link href="https://t.me/empirevn" target="_blank" rel="noopener noreferrer">
                        <Send className="h-6 w-6 text-slate-400 hover:text-sky-500" />
                    </Link>
                </div>
            </div>
            <div>
                <div className="flex justify-center items-center w-auto space-x-4">
                <a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer" className="flex items-center text-gray-600 dark:text-gray-400 hover:text-blue-600">
                    <Mail size={24} className="mr-2" />
                    <span><EMAIL></span>
                  </a>
                </div>
            </div>
        </div>
    )
}