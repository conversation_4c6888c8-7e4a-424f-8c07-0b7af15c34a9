import Link from 'next/link';
import { Facebook, Send, Mail } from 'lucide-react';

export default function Footer() {
    return (
        <div className="border-t border-slate-200 dark:border-slate-700 p-4 bg-slate-50/50 dark:bg-slate-800/50">
            {/* Social Links */}
            <div className="flex justify-center items-center space-x-6 mb-4">
                <Link
                    href="https://www.facebook.com/hoangminh9119/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                >
                    <Facebook className="h-5 w-5 text-slate-500 hover:text-blue-600 dark:text-slate-400 dark:hover:text-blue-400" />
                    <span className="sr-only">Facebook</span>
                </Link>
                <Link
                    href="https://t.me/empirevn"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 rounded-lg hover:bg-sky-50 dark:hover:bg-sky-900/20 transition-colors"
                >
                    <Send className="h-5 w-5 text-slate-500 hover:text-sky-600 dark:text-slate-400 dark:hover:text-sky-400" />
                    <span className="sr-only">Telegram</span>
                </Link>
            </div>

            {/* Email Contact */}
            <div className="text-center">
                <a
                    href="mailto:<EMAIL>"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-sm text-slate-600 dark:text-slate-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                >
                    <Mail className="h-4 w-4 mr-2" />
                    <span><EMAIL></span>
                </a>
            </div>

            {/* Copyright */}
            <div className="text-center mt-4 pt-4 border-t border-slate-200 dark:border-slate-700">
                <p className="text-xs text-slate-500 dark:text-slate-400">
                    © 2024 EmpireVN. All rights reserved.
                </p>
            </div>
        </div>
    )
}