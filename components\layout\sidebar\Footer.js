import Link from 'next/link';
import { Shield, Award, Clock } from 'lucide-react';

export default function Footer() {
    const currentYear = new Date().getFullYear();
    const yearRange = currentYear > 2024 ? `2024-${currentYear}` : '2024';

    return (
        <div className="border-t border-slate-200 dark:border-slate-700 p-4 bg-slate-50/50 dark:bg-slate-800/50">
            {/* Security Features - Mobile Version */}
            <div className="flex flex-col items-center space-y-3 mb-4">
                <div className="flex items-center space-x-1 text-xs text-slate-600 dark:text-slate-400">
                    <Shield className="h-3 w-3 text-green-600 dark:text-green-400" />
                    <span>Bảo mật SSL</span>
                </div>
                <div className="flex items-center space-x-1 text-xs text-slate-600 dark:text-slate-400">
                    <Award className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                    <span><PERSON>ia<PERSON> dịch an toàn</span>
                </div>
                <div className="flex items-center space-x-1 text-xs text-slate-600 dark:text-slate-400">
                    <Clock className="h-3 w-3 text-yellow-600 dark:text-yellow-400" />
                    <span>Hỗ trợ 24/7</span>
                </div>
            </div>

            {/* Copyright */}
            <div className="text-center pt-4 border-t border-slate-200 dark:border-slate-700">
                <p className="text-xs text-slate-500 dark:text-slate-400">
                    © {yearRange} <Link href="/" className="text-primary hover:text-primary/80 transition-colors font-medium">EmpireVN</Link>. All Rights Reserved.
                </p>
                <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                    Thiết kế và phát triển bởi EmpireVN Team
                </p>
            </div>
        </div>
    )
}