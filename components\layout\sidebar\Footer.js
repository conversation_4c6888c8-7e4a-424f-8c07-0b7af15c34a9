import Link from 'next/link';
import { Shield, Award, Clock } from 'lucide-react';

export default function Footer() {
    const currentYear = new Date().getFullYear();
    const yearRange = currentYear > 2024 ? `2024-${currentYear}` : '2024';

    return (
        <div className="border-t border-slate-200/30 dark:border-slate-700/30 p-4 bg-slate-50/20 dark:bg-slate-800/20 backdrop-blur-sm">
            {/* Security Features - Mobile Version */}
            <div className="flex items-center justify-center space-x-4 mb-4 text-sm text-foreground/90 dark:text-slate-200 font-medium">
                <span className="flex items-center space-x-1">
                    <Shield className="h-3 w-3 text-green-600 dark:text-green-400" />
                    <span>Bảo mật SSL</span>
                </span>
                <span className="text-muted-foreground dark:text-slate-500">•</span>
                <span className="flex items-center space-x-1">
                    <Award className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                    <span><PERSON><PERSON><PERSON> dịch an toàn</span>
                </span>
                <span className="text-muted-foreground dark:text-slate-500">•</span>
                <span className="flex items-center space-x-1">
                    <Clock className="h-3 w-3 text-yellow-600 dark:text-yellow-400" />
                    <span>Hỗ trợ 24/7</span>
                </span>
            </div>

            {/* Copyright */}
            <div className="text-center pt-4 border-t border-slate-200/30 dark:border-slate-700/30">
                <p className="text-xs text-slate-500 dark:text-slate-400">
                    © {yearRange} <Link href="/" className="text-primary hover:text-primary/80 transition-colors font-medium">EmpireVN</Link>. All Rights Reserved.
                </p>
                <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                    Thiết kế và phát triển bởi EmpireVN Team
                </p>
            </div>
        </div>
    )
}