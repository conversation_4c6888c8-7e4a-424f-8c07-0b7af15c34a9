'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    Activity, 
    ShoppingCart, 
    Coins, 
    TrendingUp, 
    Clock, 
    CheckCircle, 
    XCircle, 
    AlertCircle,
    RefreshCw,
    DollarSign,
    Users,
    Server
} from 'lucide-react';

export default function AdminOverview() {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [lastUpdate, setLastUpdate] = useState(null);
    const [systemStats, setSystemStats] = useState({
        buyCoinWorker: {
            isRunning: false,
            stats: {},
            runtime: 0
        },
        coinMonitorWorker: {
            isRunning: false,
            stats: {},
            runtime: 0
        },
        orders: {
            total: 0,
            created: 0,
            processing: 0,
            completed: 0,
            failed: 0,
            cancelled: 0
        },
        transactions: {
            total: 0,
            matched: 0,
            notifications: 0,
            totalCoins: 0
        }
    });

    // Fetch system overview data
    const fetchOverviewData = async () => {
        try {
            setLoading(true);
            setError(null);

            // Fetch data from multiple endpoints
            const [buyCoinWorkerRes, coinMonitorWorkerRes, ordersRes, transactionsRes] = await Promise.all([
                fetch('/api/admin/buycoin-worker'),
                fetch('/api/admin/coin-monitor'),
                fetch('/api/admin/buycoin-orders'),
                fetch('/api/admin/coin-monitor/transactions?limit=1000')
            ]);

            const [buyCoinWorkerData, coinMonitorWorkerData, ordersData, transactionsData] = await Promise.all([
                buyCoinWorkerRes.json(),
                coinMonitorWorkerRes.json(),
                ordersRes.json(),
                transactionsRes.json()
            ]);

            // Process transaction stats
            const transactions = transactionsData.success ? transactionsData.data : [];
            const transactionStats = {
                total: transactions.length,
                matched: transactions.filter(t => t.matched_order_id).length,
                notifications: transactions.filter(t => t.notification_sent).length,
                totalCoins: transactions.reduce((sum, t) => sum + parseFloat(t.coin_amount || 0), 0)
            };

            setSystemStats({
                buyCoinWorker: buyCoinWorkerData.success ?
                    { ...buyCoinWorkerData.status, stats: buyCoinWorkerData.status?.stats || {} } :
                    { isRunning: false, stats: {}, runtime: 0 },
                coinMonitorWorker: coinMonitorWorkerData.success ?
                    { ...coinMonitorWorkerData.status, stats: coinMonitorWorkerData.status?.stats || {} } :
                    { isRunning: false, stats: {}, runtime: 0 },
                orders: ordersData.success ? ordersData.stats : { total: 0, created: 0, processing: 0, completed: 0, failed: 0, cancelled: 0 },
                transactions: transactionStats
            });

            setLastUpdate(new Date());
        } catch (err) {
            setError('Failed to fetch overview data');
            console.error('Error fetching overview data:', err);
        } finally {
            setLoading(false);
        }
    };

    // Format runtime
    const formatRuntime = (seconds) => {
        if (!seconds) return '0s';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) return `${hours}h ${minutes}m`;
        if (minutes > 0) return `${minutes}m ${secs}s`;
        return `${secs}s`;
    };

    useEffect(() => {
        fetchOverviewData();
        
        // Auto refresh every 30 seconds
        const interval = setInterval(fetchOverviewData, 30000);
        return () => clearInterval(interval);
    }, []);

    if (loading && !systemStats.orders.total) {
        return (
            <div className="space-y-6">
                <div className="text-center py-8">
                    <RefreshCw className="h-8 w-8 mx-auto mb-2 text-gray-400 animate-spin" />
                    <p className="text-gray-500">Loading system overview...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">System Overview</h2>
                    <p className="text-muted-foreground">
                        Tổng quan hệ thống và trạng thái các worker
                    </p>
                </div>
                
                <div className="flex items-center gap-4">
                    {lastUpdate && (
                        <div className="text-sm text-muted-foreground">
                            Last updated: {lastUpdate.toLocaleTimeString()}
                        </div>
                    )}
                    <Button onClick={fetchOverviewData} disabled={loading} variant="outline" size="sm">
                        <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                </div>
            </div>

            {/* Error Alert */}
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Workers Status */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* BuyCoin Worker */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <ShoppingCart className="w-5 h-5" />
                            BuyCoin Order Worker
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${systemStats.buyCoinWorker?.isRunning ? 'bg-green-500' : 'bg-red-500'}`}></div>
                            <Badge variant={systemStats.buyCoinWorker?.isRunning ? "default" : "secondary"}>
                                {systemStats.buyCoinWorker?.isRunning ? "Running" : "Stopped"}
                            </Badge>
                        </div>

                        {systemStats.buyCoinWorker?.isRunning && (
                            <div className="text-sm text-muted-foreground">
                                Runtime: {formatRuntime(systemStats.buyCoinWorker?.runtime || 0)}
                            </div>
                        )}

                        {systemStats.buyCoinWorker?.stats && (
                            <div className="grid grid-cols-2 gap-2 text-sm">
                                <div>
                                    <div className="font-medium">Processed</div>
                                    <div className="text-blue-600">{systemStats.buyCoinWorker.stats.processed || 0}</div>
                                </div>
                                <div>
                                    <div className="font-medium">Errors</div>
                                    <div className="text-red-600">{systemStats.buyCoinWorker.stats.errors || 0}</div>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Coin Monitor Worker */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Activity className="w-5 h-5" />
                            Coin Monitor Worker
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${systemStats.coinMonitorWorker?.isRunning ? 'bg-green-500' : 'bg-red-500'}`}></div>
                            <Badge variant={systemStats.coinMonitorWorker?.isRunning ? "default" : "secondary"}>
                                {systemStats.coinMonitorWorker?.isRunning ? "Running" : "Stopped"}
                            </Badge>
                        </div>

                        {systemStats.coinMonitorWorker?.isRunning && (
                            <div className="text-sm text-muted-foreground">
                                Runtime: {formatRuntime(systemStats.coinMonitorWorker?.runtime || 0)}
                            </div>
                        )}

                        {systemStats.coinMonitorWorker?.stats && (
                            <div className="grid grid-cols-2 gap-2 text-sm">
                                <div>
                                    <div className="font-medium">Checks</div>
                                    <div className="text-blue-600">{systemStats.coinMonitorWorker.stats.totalChecks || 0}</div>
                                </div>
                                <div>
                                    <div className="font-medium">Found</div>
                                    <div className="text-green-600">{systemStats.coinMonitorWorker.stats.transactionsFound || 0}</div>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>

            {/* System Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <ShoppingCart className="w-4 h-4 text-blue-600" />
                            <div className="text-sm font-medium">Total Orders</div>
                        </div>
                        <div className="text-2xl font-bold text-blue-600 mt-2">
                            {systemStats.orders.total}
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-green-600" />
                            <div className="text-sm font-medium">Completed</div>
                        </div>
                        <div className="text-2xl font-bold text-green-600 mt-2">
                            {systemStats.orders.completed}
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <Coins className="w-4 h-4 text-orange-600" />
                            <div className="text-sm font-medium">Total Coins</div>
                        </div>
                        <div className="text-2xl font-bold text-orange-600 mt-2">
                            {systemStats.transactions.totalCoins.toFixed(2)}
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <Activity className="w-4 h-4 text-purple-600" />
                            <div className="text-sm font-medium">Transactions</div>
                        </div>
                        <div className="text-2xl font-bold text-purple-600 mt-2">
                            {systemStats.transactions.total}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Orders Breakdown */}
            <Card>
                <CardHeader>
                    <CardTitle>Orders Status Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                        <div className="text-center">
                            <div className="text-2xl font-bold text-gray-600">{systemStats.orders.created}</div>
                            <div className="text-sm text-muted-foreground">Created</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-blue-600">{systemStats.orders.processing}</div>
                            <div className="text-sm text-muted-foreground">Processing</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-green-600">{systemStats.orders.completed}</div>
                            <div className="text-sm text-muted-foreground">Completed</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-red-600">{systemStats.orders.failed}</div>
                            <div className="text-sm text-muted-foreground">Failed</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-gray-600">{systemStats.orders.cancelled}</div>
                            <div className="text-sm text-muted-foreground">Cancelled</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-purple-600">{systemStats.transactions.matched}</div>
                            <div className="text-sm text-muted-foreground">Matched</div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
