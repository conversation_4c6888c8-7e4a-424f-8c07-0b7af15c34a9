'use client'
import { useState } from "react"
import { ItemChoose } from "@/components/features/etopfun/ItemChoose"
import { ItemGrid } from "@/components/features/etopfun/ItemGrid"
import  OrderForm  from "@/components/features/etopfun/OrderForm"
import CardInfo from "@/components/ui/card-info"

export default function Etopfun({ items, banks, steamId, rate }) {
    const [selectedItems, setSelectedItems] = useState([])
    const handleItemSelect = (item) => {
        setSelectedItems((prev) => [...prev, item])
    }
    const handleItemDeselect = (item) => {
        setSelectedItems((prev) => prev.filter((i) => i.id !== item.id))
    }
    const totalValue = selectedItems.reduce((sum, item) => sum + item.value, 0)
    return (
        <CardInfo>
            <div className="grid md:grid-cols-2 gap-6">
                <div className="dark:border dark:shadow-none shadow-slate-200-100 shadow-md p-3 md:p-6 rounded-lg">
                    <ItemChoose selectedItems={selectedItems}
                        onItemDeselect={handleItemDeselect}>
                    </ItemChoose>
                    <OrderForm
                        selectedItems={selectedItems}
                        totalValue={totalValue}
                        steamId={steamId}
                        banks={banks}
                        rate={rate}
                    ></OrderForm>
                </div>
                <ItemGrid
                    items={items}
                    selectedItems={selectedItems}
                    onItemSelect={handleItemSelect}
                ></ItemGrid>
            </div>
        </CardInfo>
    )

}