'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
    Activity, 
    ShoppingCart, 
    Play, 
    Square, 
    RotateCcw, 
    RefreshCw, 
    AlertCircle,
    CheckCircle,
    TestTube,
    Clock
} from 'lucide-react';

export default function WorkersManagement() {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [lastUpdate, setLastUpdate] = useState(null);
    const [workers, setWorkers] = useState({
        buyCoinWorker: {
            isRunning: false,
            stats: {},
            runtime: 0,
            loading: false
        },
        coinMonitorWorker: {
            isRunning: false,
            stats: {},
            runtime: 0,
            loading: false
        }
    });

    // Fetch workers status
    const fetchWorkersStatus = async () => {
        try {
            setLoading(true);
            setError(null);

            const [buyCoinRes, coinMonitorRes] = await Promise.all([
                fetch('/api/admin/buycoin-worker'),
                fetch('/api/admin/coin-monitor')
            ]);

            const [buyCoinData, coinMonitorData] = await Promise.all([
                buyCoinRes.json(),
                coinMonitorRes.json()
            ]);

            setWorkers({
                buyCoinWorker: {
                    ...(buyCoinData.success ? buyCoinData.status : { isRunning: false, stats: {}, runtime: 0 }),
                    stats: buyCoinData.success ? (buyCoinData.status?.stats || {}) : {},
                    loading: false
                },
                coinMonitorWorker: {
                    ...(coinMonitorData.success ? coinMonitorData.status : { isRunning: false, stats: {}, runtime: 0 }),
                    stats: coinMonitorData.success ? (coinMonitorData.status?.stats || {}) : {},
                    loading: false
                }
            });

            setLastUpdate(new Date());
        } catch (err) {
            setError('Failed to fetch workers status');
            console.error('Error fetching workers status:', err);
        } finally {
            setLoading(false);
        }
    };

    // Control BuyCoin Worker
    const controlBuyCoinWorker = async (action) => {
        setWorkers(prev => ({
            ...prev,
            buyCoinWorker: { ...prev.buyCoinWorker, loading: true }
        }));

        try {
            const response = await fetch('/api/admin/buycoin-worker', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action })
            });

            const data = await response.json();
            
            if (data.success) {
                setTimeout(fetchWorkersStatus, 1000);
            } else {
                setError(`Failed to ${action} BuyCoin worker: ${data.error}`);
            }
        } catch (err) {
            setError(`Error ${action} BuyCoin worker`);
            console.error(`Error ${action} BuyCoin worker:`, err);
        } finally {
            setWorkers(prev => ({
                ...prev,
                buyCoinWorker: { ...prev.buyCoinWorker, loading: false }
            }));
        }
    };

    // Control Coin Monitor Worker
    const controlCoinMonitorWorker = async (action) => {
        setWorkers(prev => ({
            ...prev,
            coinMonitorWorker: { ...prev.coinMonitorWorker, loading: true }
        }));

        try {
            const response = await fetch('/api/admin/coin-monitor', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ action })
            });

            const data = await response.json();
            
            if (data.success) {
                setTimeout(fetchWorkersStatus, 1000);
            } else {
                setError(`Failed to ${action} Coin Monitor worker: ${data.error}`);
            }
        } catch (err) {
            setError(`Error ${action} Coin Monitor worker`);
            console.error(`Error ${action} Coin Monitor worker:`, err);
        } finally {
            setWorkers(prev => ({
                ...prev,
                coinMonitorWorker: { ...prev.coinMonitorWorker, loading: false }
            }));
        }
    };

    // Format runtime
    const formatRuntime = (seconds) => {
        if (!seconds) return '0s';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) return `${hours}h ${minutes}m`;
        if (minutes > 0) return `${minutes}m ${secs}s`;
        return `${secs}s`;
    };

    useEffect(() => {
        fetchWorkersStatus();
        
        // Auto refresh every 10 seconds
        const interval = setInterval(fetchWorkersStatus, 10000);
        return () => clearInterval(interval);
    }, []);

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Workers Management</h2>
                    <p className="text-muted-foreground">
                        Quản lý và điều khiển các worker trong hệ thống
                    </p>
                </div>
                
                <div className="flex items-center gap-4">
                    {lastUpdate && (
                        <div className="text-sm text-muted-foreground">
                            Last updated: {lastUpdate.toLocaleTimeString()}
                        </div>
                    )}
                    <Button onClick={fetchWorkersStatus} disabled={loading} variant="outline" size="sm">
                        <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                </div>
            </div>

            {/* Error Alert */}
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* BuyCoin Order Worker */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <ShoppingCart className="w-5 h-5" />
                        BuyCoin Order Worker
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Status */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <div className={`w-3 h-3 rounded-full ${workers.buyCoinWorker?.isRunning ? 'bg-green-500' : 'bg-red-500'}`}></div>
                            <div>
                                <div className="font-semibold">
                                    Status: {workers.buyCoinWorker?.isRunning ? 'Running' : 'Stopped'}
                                </div>
                                {workers.buyCoinWorker?.isRunning && (
                                    <div className="text-sm text-muted-foreground">
                                        Runtime: {formatRuntime(workers.buyCoinWorker?.runtime || 0)}
                                    </div>
                                )}
                            </div>
                        </div>

                        <Badge variant={workers.buyCoinWorker?.isRunning ? "default" : "secondary"}>
                            {workers.buyCoinWorker?.isRunning ? "Active" : "Inactive"}
                        </Badge>
                    </div>

                    {/* Statistics */}
                    {workers.buyCoinWorker?.stats && (
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <div className="font-medium">Processed</div>
                                <div className="text-blue-600 text-lg">{workers.buyCoinWorker.stats?.processed || 0}</div>
                            </div>
                            <div>
                                <div className="font-medium">Completed</div>
                                <div className="text-green-600 text-lg">{workers.buyCoinWorker.stats?.completed || 0}</div>
                            </div>
                            <div>
                                <div className="font-medium">Failed</div>
                                <div className="text-red-600 text-lg">{workers.buyCoinWorker.stats?.failed || 0}</div>
                            </div>
                            <div>
                                <div className="font-medium">Errors</div>
                                <div className="text-orange-600 text-lg">{workers.buyCoinWorker.stats?.errors || 0}</div>
                            </div>
                        </div>
                    )}

                    {/* Controls */}
                    <div className="flex flex-wrap gap-2">
                        <Button
                            onClick={() => controlBuyCoinWorker('start')}
                            disabled={workers.buyCoinWorker?.loading || workers.buyCoinWorker?.isRunning}
                            size="sm"
                        >
                            <Play className="w-4 h-4 mr-1" />
                            Start
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => controlBuyCoinWorker('stop')}
                            disabled={workers.buyCoinWorker?.loading || !workers.buyCoinWorker?.isRunning}
                            size="sm"
                        >
                            <Square className="w-4 h-4 mr-1" />
                            Stop
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => controlBuyCoinWorker('restart')}
                            disabled={workers.buyCoinWorker?.loading}
                            size="sm"
                        >
                            <RotateCcw className="w-4 h-4 mr-1" />
                            Restart
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Coin Monitor Worker */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Activity className="w-5 h-5" />
                        Coin Monitor Worker
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Status */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <div className={`w-3 h-3 rounded-full ${workers.coinMonitorWorker?.isRunning ? 'bg-green-500' : 'bg-red-500'}`}></div>
                            <div>
                                <div className="font-semibold">
                                    Status: {workers.coinMonitorWorker?.isRunning ? 'Running' : 'Stopped'}
                                </div>
                                {workers.coinMonitorWorker?.isRunning && (
                                    <div className="text-sm text-muted-foreground">
                                        Runtime: {formatRuntime(workers.coinMonitorWorker?.runtime || 0)}
                                    </div>
                                )}
                            </div>
                        </div>

                        <Badge variant={workers.coinMonitorWorker?.isRunning ? "default" : "secondary"}>
                            {workers.coinMonitorWorker?.isRunning ? "Active" : "Inactive"}
                        </Badge>
                    </div>

                    {/* Statistics */}
                    {workers.coinMonitorWorker?.stats && (
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <div className="font-medium">Total Checks</div>
                                <div className="text-blue-600 text-lg">{workers.coinMonitorWorker.stats?.totalChecks || 0}</div>
                            </div>
                            <div>
                                <div className="font-medium">Transactions Found</div>
                                <div className="text-green-600 text-lg">{workers.coinMonitorWorker.stats?.transactionsFound || 0}</div>
                            </div>
                            <div>
                                <div className="font-medium">Notifications Sent</div>
                                <div className="text-purple-600 text-lg">{workers.coinMonitorWorker.stats?.notificationsSent || 0}</div>
                            </div>
                            <div>
                                <div className="font-medium">Errors</div>
                                <div className="text-red-600 text-lg">{workers.coinMonitorWorker.stats?.errors || 0}</div>
                            </div>
                        </div>
                    )}

                    {/* Error Count Warning */}
                    {workers.coinMonitorWorker?.errorCount > 0 && (
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>
                                Worker has {workers.coinMonitorWorker.errorCount} consecutive errors.
                                Max errors before auto-stop: {workers.coinMonitorWorker.maxErrors}
                            </AlertDescription>
                        </Alert>
                    )}

                    {/* Controls */}
                    <div className="flex flex-wrap gap-2">
                        <Button
                            onClick={() => controlCoinMonitorWorker('start')}
                            disabled={workers.coinMonitorWorker?.loading || workers.coinMonitorWorker?.isRunning}
                            size="sm"
                        >
                            <Play className="w-4 h-4 mr-1" />
                            Start
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => controlCoinMonitorWorker('stop')}
                            disabled={workers.coinMonitorWorker?.loading || !workers.coinMonitorWorker?.isRunning}
                            size="sm"
                        >
                            <Square className="w-4 h-4 mr-1" />
                            Stop
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => controlCoinMonitorWorker('restart')}
                            disabled={workers.coinMonitorWorker?.loading}
                            size="sm"
                        >
                            <RotateCcw className="w-4 h-4 mr-1" />
                            Restart
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => controlCoinMonitorWorker('test-telegram')}
                            disabled={workers.coinMonitorWorker?.loading}
                            size="sm"
                        >
                            <TestTube className="w-4 h-4 mr-1" />
                            Test Telegram
                        </Button>
                        <Button
                            variant="outline"
                            onClick={() => controlCoinMonitorWorker('validate-config')}
                            disabled={workers.coinMonitorWorker?.loading}
                            size="sm"
                        >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Validate Config
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
