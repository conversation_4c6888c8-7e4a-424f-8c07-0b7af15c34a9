import { execute } from '@/lib/db';
import { NextResponse } from 'next/server';

// GET - Fetch specific buycoin order
export async function GET(request, { params }) {
    try {
        const { id } = params;
        
        const [orders] = await execute(`
            SELECT 
                id,
                order_id,
                steam_id,
                coin_amount,
                vnd_amount,
                bank_code,
                account_number,
                account_name,
                status,
                admin_note,
                processed_by,
                processed_at,
                created_at,
                updated_at
            FROM buycoin 
            WHERE id = ?
        `, [id]);
        
        if (orders.length === 0) {
            return NextResponse.json({
                success: false,
                error: 'Order not found'
            }, { status: 404 });
        }
        
        return NextResponse.json({
            success: true,
            order: orders[0]
        });
        
    } catch (error) {
        console.error('Error fetching buycoin order:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to fetch order'
        }, { status: 500 });
    }
}

// PUT - Update buycoin order
export async function PUT(request, { params }) {
    try {
        const { id } = params;
        const body = await request.json();
        const {
            status,
            admin_note,
            processed_by
        } = body;
        
        // Validate status
        const validStatuses = ['created', 'processing', 'completed', 'failed', 'cancelled'];
        if (status && !validStatuses.includes(status)) {
            return NextResponse.json({
                success: false,
                error: 'Invalid status'
            }, { status: 400 });
        }
        
        // Check if order exists
        const [existingOrder] = await execute(
            'SELECT id, status as current_status FROM buycoin WHERE id = ?',
            [id]
        );
        
        if (existingOrder.length === 0) {
            return NextResponse.json({
                success: false,
                error: 'Order not found'
            }, { status: 404 });
        }
        
        // Build update query
        let updateFields = [];
        let updateValues = [];
        
        if (status) {
            updateFields.push('status = ?');
            updateValues.push(status);
        }
        
        if (admin_note !== undefined) {
            updateFields.push('admin_note = ?');
            updateValues.push(admin_note);
        }
        
        if (processed_by) {
            updateFields.push('processed_by = ?');
            updateValues.push(processed_by);
        }
        
        // Add processed_at if status is being changed to processing, completed, failed, or cancelled
        if (status && ['processing', 'completed', 'failed', 'cancelled'].includes(status)) {
            updateFields.push('processed_at = NOW()');
        }
        
        // Always update updated_at
        updateFields.push('updated_at = NOW()');
        
        if (updateFields.length === 1) { // Only updated_at
            return NextResponse.json({
                success: false,
                error: 'No fields to update'
            }, { status: 400 });
        }
        
        // Execute update
        updateValues.push(id);
        const [result] = await execute(`
            UPDATE buycoin 
            SET ${updateFields.join(', ')}
            WHERE id = ?
        `, updateValues);
        
        if (result.affectedRows === 0) {
            return NextResponse.json({
                success: false,
                error: 'Failed to update order'
            }, { status: 500 });
        }
        
        // Get updated order
        const [updatedOrder] = await execute(`
            SELECT 
                id,
                order_id,
                steam_id,
                coin_amount,
                vnd_amount,
                bank_code,
                account_number,
                account_name,
                status,
                admin_note,
                processed_by,
                processed_at,
                created_at,
                updated_at
            FROM buycoin 
            WHERE id = ?
        `, [id]);
        
        return NextResponse.json({
            success: true,
            message: 'Order updated successfully',
            order: updatedOrder[0]
        });
        
    } catch (error) {
        console.error('Error updating buycoin order:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to update order'
        }, { status: 500 });
    }
}

// DELETE - Delete buycoin order (soft delete by marking as cancelled)
export async function DELETE(request, { params }) {
    try {
        const { id } = params;
        
        // Check if order exists
        const [existingOrder] = await execute(
            'SELECT id, status FROM buycoin WHERE id = ?',
            [id]
        );
        
        if (existingOrder.length === 0) {
            return NextResponse.json({
                success: false,
                error: 'Order not found'
            }, { status: 404 });
        }
        
        // Soft delete by marking as cancelled
        const [result] = await execute(`
            UPDATE buycoin 
            SET status = 'cancelled',
                admin_note = CONCAT(COALESCE(admin_note, ''), '\nOrder deleted by admin'),
                processed_by = 'admin',
                processed_at = NOW(),
                updated_at = NOW()
            WHERE id = ?
        `, [id]);
        
        if (result.affectedRows === 0) {
            return NextResponse.json({
                success: false,
                error: 'Failed to delete order'
            }, { status: 500 });
        }
        
        return NextResponse.json({
            success: true,
            message: 'Order deleted successfully'
        });
        
    } catch (error) {
        console.error('Error deleting buycoin order:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to delete order'
        }, { status: 500 });
    }
}
