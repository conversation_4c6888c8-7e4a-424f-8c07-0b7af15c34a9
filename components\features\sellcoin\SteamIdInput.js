"use client";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { User } from "lucide-react";

export default function SteamIdInput({ steamId, register, errors, state }) {
  return (
    <div className="space-y-2 md:space-y-3">
      <Label htmlFor="steamId" className="flex items-center space-x-2 text-base font-medium">
        <User className="h-4 w-4 text-blue-600" />
        <span>Steam ID</span>
      </Label>
      <div className="relative">
        <Input
          defaultValue={steamId}
          id="steamId"
          type="text"
          {...register("steamId")}
          placeholder="Nhập Steam ID của bạn"
          className="h-12 pl-4 text-lg border-2 border-gray-200 dark:border-gray-600 focus:border-blue-500 transition-all duration-200"
        />
      </div>
      {errors.steamId && (
        <p className="text-sm text-red-500 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{errors.steamId.message}</span>
        </p>
      )}
      {state?.errors?.steamId && (
        <p className="text-sm text-red-500 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{state.errors.steamId[0]}</span>
        </p>
      )}
    </div>
  );
}
