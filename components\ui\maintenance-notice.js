import { AlertTriangle, Clock, Settings } from "lucide-react";

export default function MaintenanceNotice({ 
  isVisible = true, 
  message = "Hệ thống đang trong quá trình bảo trì. Vui lòng thử lại sau.",
  type = "warning" // "warning", "info", "error"
}) {
  if (!isVisible) return null;

  const getTypeStyles = () => {
    switch (type) {
      case "error":
        return {
          bg: "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800",
          text: "text-red-800 dark:text-red-200",
          icon: "text-red-600 dark:text-red-400"
        };
      case "info":
        return {
          bg: "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800",
          text: "text-blue-800 dark:text-blue-200",
          icon: "text-blue-600 dark:text-blue-400"
        };
      default: // warning
        return {
          bg: "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800",
          text: "text-yellow-800 dark:text-yellow-200",
          icon: "text-yellow-600 dark:text-yellow-400"
        };
    }
  };

  const styles = getTypeStyles();
  
  const getIcon = () => {
    switch (type) {
      case "error":
        return <AlertTriangle className="h-5 w-5" />;
      case "info":
        return <Settings className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  return (
    <div className={`border rounded-lg p-4 mb-6 ${styles.bg}`}>
      <div className="flex items-start space-x-3">
        <div className={`flex-shrink-0 ${styles.icon}`}>
          {getIcon()}
        </div>
        <div className="flex-1">
          <h3 className={`text-sm font-medium ${styles.text}`}>
            Thông báo bảo trì
          </h3>
          <p className={`mt-1 text-sm ${styles.text}`}>
            {message}
          </p>
        </div>
      </div>
    </div>
  );
}
