RETURN_URL = http://localhost:3000/login/steam/callback
REALM = http://localhost:3000
API_SERVER = http://localhost:8888/api/

SERVER = localhost

JWT_SECRET = 3213213fsadsadsadsadsadsaw232sadas

AUTH_SECRET= W5M794ZNAmbXqYObeiZaULtb27xeApAfFq6u76WUTvw=

SECRET_KEY= 9a02115a835ee03d5fb83cd8a468ea33e4090aaa2c87f53c9fa54512b5ef4db8dc656c82a315fa0c785c08b0134716b81d6cd0153d2
GOOGLE_CLIENT_ID= 216043096490-0unhjvcs2tfglcqqspco8d18gsiiar9f.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET= 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
GOOGLE_REDIRECT_URI= http://localhost:3000/login/google/callback

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=Minhhoang93@
DB_NAME=empirevn

# CSGOEmpire API Configuration
CSGOEMPIRE_BEARER_TOKEN=100bd6d066081c97c45dbd0eefa1668a

# Telegram Bot Configuration (Primary notification service)
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=1725032665

# Zalo Configuration (Legacy - no longer used)
# ZALO_PHONE=your_zalo_phone_number
# ZALO_PASSWORD=your_zalo_password
# ZALO_TARGET_USER_ID=target_user_id_to_send_messages