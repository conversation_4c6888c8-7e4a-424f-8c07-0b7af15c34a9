"use client";
import { useFormStatus } from "react-dom";
import { But<PERSON> } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/enhanced-loading";
import { ShoppingCart } from "lucide-react";

export default function SubmitButton() {
  const { pending } = useFormStatus();

  return (
    <Button
      type="submit"
      className="w-full h-12 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
      disabled={pending}
    >
      {pending ? (
        <>
          <LoadingSpinner size="default" className="mr-2" />
          <PERSON>ang xử lý...
        </>
      ) : (
        <>
          <ShoppingCart className="mr-2 h-5 w-5" />
          <PERSON><PERSON>
        </>
      )}
    </Button>
  );
}
