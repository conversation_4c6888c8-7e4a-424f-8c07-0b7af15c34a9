import Footer from "@/components/layout/footer/Footer";
import "@/app/globals.css";
import { ThemeProvider } from "@/components/providers/theme-provider"
import Headers from "@/components/layout/header/Header";
import { Toaster } from "@/components/ui/toaster"
import SEO from "@/components/common/SEO";
import AnimatedBackground from "@/components/ui/animated-background";
import FloatingElements from "@/components/ui/floating-elements";

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`antialiased min-h-screen flex flex-col relative overflow-x-hidden`}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem disableTransitionOnChange>


          {/* Animated Background */}
          <AnimatedBackground />
          <FloatingElements />

          {/* Main Layout */}
          <div className="relative z-20">
            <Headers />
            <Toaster />
            <main className="flex-grow relative">
              {children}
            </main>
            <Footer />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
