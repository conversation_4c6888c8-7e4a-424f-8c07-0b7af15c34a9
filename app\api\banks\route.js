import { fetchBanks } from '@/service/bank-service';
import { NextResponse } from 'next/server';

export async function GET() {
    try {
        const banks = await fetchBanks();

        return NextResponse.json({
            success: true,
            data: banks,
            total: banks.length
        });
    } catch (error) {
        console.error('Error in banks API:', error);

        return NextResponse.json({
            success: false,
            error: error.message,
            data: []
        }, { status: 500 });
    }
}

// Enable caching for this route
export const revalidate = 86400; // 24 hours
