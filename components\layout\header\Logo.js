"use strict";

export function Logo({ className = "h-10 w-10" }) {
  return (
    <div className={`${className} relative`}>
      {/* Background gradient circle */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 rounded-xl opacity-90"></div>

      {/* Main logo SVG */}
      <svg
        className="relative z-10 w-full h-full p-2 text-white"
        fill="currentColor"
        viewBox="0 0 24 24"
      >
        {/* Crown/Empire symbol */}
        <path d="M5 16L3 6l5.5 4L12 4l3.5 6L21 6l-2 10H5z" />
        {/* Coin/Circle elements */}
        <circle cx="8" cy="18" r="1.5" opacity="0.8" />
        <circle cx="12" cy="18" r="1.5" opacity="0.8" />
        <circle cx="16" cy="18" r="1.5" opacity="0.8" />
      </svg>

      {/* Shine effect */}
      <div className="absolute inset-0 bg-gradient-to-tr from-white/20 to-transparent rounded-xl"></div>
    </div>
  );
}

export default function NavbarLogo({ className = "" }) {
  return (
    <div className={`flex items-center space-x-3 ${className}`}>
      <Logo className="h-10 w-10" />
      <div className=" sm:block">
        <div className="flex items-baseline space-x-1">
          <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent dark:from-blue-400 dark:via-purple-400 dark:to-indigo-400">
            Empire
          </span>
          <span className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent dark:from-orange-400 dark:to-red-400">
            VN
          </span>
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 -mt-1 font-medium">
          Coin Trading Platform
        </p>
      </div>
    </div>
  );
};