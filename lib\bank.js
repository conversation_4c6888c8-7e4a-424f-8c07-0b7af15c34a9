function fetchWithTimeout(url, options = {}, timeout = 20000) {
    return new Promise((resolve, reject) => {
        const timer = setTimeout(() => reject(new Error('Request timeout')), timeout);
        fetch(url, options)
            .then((res) => {
                clearTimeout(timer);
                resolve(res);
            })
            .catch((err) => {
                clearTimeout(timer);
                reject(err);
            });
    });
}

class ACB {
    constructor(username, password) {
        this.username = username;
        this.password = password;
        this.clientId = '';
        this.deviceId = this.generateImei().toUpperCase();
        this.URL = {
            LOGIN: 'https://apiapp.acb.com.vn/mb/v2/auth/tokens',
            GET_TRANS:
                'https://apiapp.acb.com.vn/mb/legacy/ss/cs/bankservice/saving/tx-history',
        };
    }

    async login() {
        const headers = {
            'Content-Type': 'application/json; charset=utf-8',
            Host: 'apiapp.acb.com.vn',
            accept: 'application/json',
        };

        const data = {
            clientId: this.clientId,
            deviceId: this.deviceId,
            username: this.username,
            password: this.password,
        };

        const res = await fetchWithTimeout(this.URL.LOGIN, {
            method: 'POST',
            headers,
            body: JSON.stringify(data),
        });

        if (!res.ok) {
            throw new Error(`Login failed: ${res.status}`);
        }

        return res.json();
    }

    async LSGD(accountNo, rows, token) {
        const headers = {
            'Content-Type': 'application/json',
            Host: 'apiapp.acb.com.vn',
            Authorization: `bearer ${token}`,
            'User-Agent': 'ACB-MBA/5 CFNetwork/1333.0.4 Darwin/21.5.0',
            'Accept-Language': 'vi',
            'x-app-version': '3.7.0',
        };

        const url = `${this.URL.GET_TRANS}?maxRows=${rows}&account=${accountNo}`;

        const res = await fetchWithTimeout(url, {
            method: 'GET',
            headers,
        });

        if (!res.ok) {
            throw new Error(`Get transactions failed: ${res.status}`);
        }

        const result = await res.json();
        return result?.data || [];
    }

    generateImei() {
        return `${this.generateRandomString(8)}-${this.generateRandomString(
            4
        )}-${this.generateRandomString(4)}-${this.generateRandomString(
            4
        )}-${this.generateRandomString(12)}`;
    }

    generateRandomString(length) {
        const characters =
            '0123456789zxcvbnmlkjhgfdsaqwertyuiopZXCVBNMLKJHGFDSAQWERTYUIOP';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return result;
    }
}

export const getTransaction = async (rows = 20) => {
    const username = process.env.BANK_USERNAME;
    const password = process.env.BANK_PASSWORD;
    const accountNumber = process.env.BANK_ACCOUNT_NUMBER;

    const acb = new ACB(username, password);
    acb.clientId = 'iuSuHYVufIUuNIREV0FB9EoLn9kHsDbm';

    try {
        const login = await acb.login();
        const token = login.accessToken;

        const data = await acb.LSGD(accountNumber, rows, token);
        return data;
    } catch (err) {
        console.error('Error:', err.message);
        return [];
    }
};