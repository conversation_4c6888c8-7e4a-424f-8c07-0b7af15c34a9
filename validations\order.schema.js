import { z } from "zod";

export const orderSchema = z.object({
    steamId: z
        .string()
        .regex(/^\d{17}$/, { message: "Steam ID phải là chuỗi 17 chữ số" }),
    coin: z
        .string()
        .regex(/^\d+(\.\d{1,2})?$/, { message: "Coin phải là số thực với tối đa 2 chữ số thập phân" }),
    amount: z
        .string()
        .regex(/^\d+$/, { message: "VND phải là một số nguyên" }),
    bankNo: z
        .string()
        .nonempty({ message: "Bank không được bỏ trống" }),
});
