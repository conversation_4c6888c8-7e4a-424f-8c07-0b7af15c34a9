"use client";
import { User, Coins, DollarSign } from "lucide-react";

export default function BuyCoinInfo({ data, currentStatus = null }) {
  if (!data) {
    return (
      <div className="text-center text-gray-500">
        Không có thông tin đơn hàng
      </div>
    );
  }

  // Use current status if provided, otherwise fall back to original data status
  const displayStatus = currentStatus || data.status;

  return (
    <div className="space-y-3">
      {/* Order ID */}
      <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-slate-700/50 rounded-lg">
        <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Mã đơn hàng:</span>
        <span className="text-sm font-bold text-blue-700 dark:text-blue-300">{data.orderId}</span>
      </div>

      {/* Steam ID */}
      <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-slate-700/50 rounded-lg">
        <div className="flex items-center space-x-2">
          <User className="h-3 w-3 text-blue-600 dark:text-blue-400" />
          <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Steam ID chuyển:</span>
        </div>
        <span className="text-sm font-bold text-blue-700 dark:text-blue-300">{data.steamId}</span>
      </div>

      {/* Coin Amount */}
      <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-slate-700/50 rounded-lg">
        <div className="flex items-center space-x-2">
          <Coins className="h-3 w-3 text-yellow-600 dark:text-yellow-400" />
          <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Số coin gửi:</span>
        </div>
        <span className="text-sm font-bold text-yellow-700 dark:text-yellow-300">{data.coinAmount} coin</span>
      </div>

      {/* VND Amount */}
      <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-slate-700/50 rounded-lg">
        <div className="flex items-center space-x-2">
          <DollarSign className="h-3 w-3 text-green-600 dark:text-green-400" />
          <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Số tiền nhận:</span>
        </div>
        <span className="text-sm font-bold text-green-700 dark:text-green-300">
          {Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(data.vndAmount)}
        </span>
      </div>

      {/* Status */}
      <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-slate-700/50 rounded-lg">
        <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Trạng thái:</span>
        <span className={`text-sm font-bold ${getStatusColor(displayStatus)}`}>
          {getStatusText(displayStatus)}
        </span>
      </div>

      {/* Created At */}
      {data.created_at && (
        <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-slate-700/50 rounded-lg">
          <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Thời gian tạo:</span>
          <span className="text-sm font-bold text-gray-700 dark:text-gray-300">
            {new Date(data.created_at).toLocaleString('vi-VN')}
          </span>
        </div>
      )}
    </div>
  );
}

function getStatusColor(status) {
  switch (status) {
    case 'created':
      return 'text-blue-600 dark:text-blue-400';
    case 'processing':
      return 'text-orange-600 dark:text-orange-400';
    case 'completed':
      return 'text-green-600 dark:text-green-400';
    case 'failed':
      return 'text-red-600 dark:text-red-400';
    case 'cancelled':
      return 'text-gray-600 dark:text-gray-400';
    default:
      return 'text-gray-600 dark:text-gray-400';
  }
}

function getStatusText(status) {
  switch (status) {
    case 'created':
      return 'Đang chờ gửi coin';
    case 'processing':
      return 'Đang xử lý';
    case 'completed':
      return 'Hoàn thành';
    case 'failed':
      return 'Thất bại';
    case 'cancelled':
      return 'Đã hủy';
    default:
      return 'Không xác định';
  }
}
