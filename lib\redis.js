import { createClient } from 'redis';

let client;

function createRedisClient() {
  return createClient({
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    socket: {
      connectTimeout: 5000,
      lazyConnect: true,
    },
    retry_strategy: (options) => {
      if (options.error && options.error.code === 'ECONNREFUSED') {
        // End reconnecting on a specific error and flush all commands with
        // a individual error
        return new Error('The server refused the connection');
      }
      if (options.total_retry_time > 1000 * 60 * 60) {
        // End reconnecting after a specific timeout and flush all commands
        // with a individual error
        return new Error('Retry time exhausted');
      }
      if (options.attempt > 10) {
        // End reconnecting with built in error
        return undefined;
      }
      // reconnect after
      return Math.min(options.attempt * 100, 3000);
    },
  });
}

export function getRedisClient() {
  if (client) return client;

  // Lưu vào globalThis để tránh tạo nhiều client khi hot-reload (Next.js dev mode)
  if (!globalThis.__REDIS_CLIENT__) {
    globalThis.__REDIS_CLIENT__ = createRedisClient();
  }
  client = globalThis.__REDIS_CLIENT__;
  return client;
}

export async function connectRedis() {
  const redisClient = getRedisClient();
  
  if (!redisClient.isOpen) {
    try {
      await redisClient.connect();
      console.log('Redis connected successfully');
    } catch (error) {
      console.error('Redis connection failed:', error);
      throw error;
    }
  }
  
  return redisClient;
}

export async function disconnectRedis() {
  if (client && client.isOpen) {
    await client.disconnect();
    client = null;
    globalThis.__REDIS_CLIENT__ = null;
  }
}

// Cache utilities
export async function setCache(key, value, ttlSeconds = 5184000) { // 2 tháng mặc định
  try {
    const redisClient = await connectRedis();
    await redisClient.setEx(key, ttlSeconds, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error('Error setting cache:', error);
    return false;
  }
}

export async function getCache(key) {
  try {
    const redisClient = await connectRedis();
    const value = await redisClient.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error('Error getting cache:', error);
    return null;
  }
}

export async function deleteCache(key) {
  try {
    const redisClient = await connectRedis();
    await redisClient.del(key);
    return true;
  } catch (error) {
    console.error('Error deleting cache:', error);
    return false;
  }
}

// Rate limiting utilities
export async function checkRateLimit(key, limit = 5, windowSeconds = 60) {
  try {
    const redisClient = await connectRedis();
    const current = await redisClient.incr(key);
    
    if (current === 1) {
      // First request, set expiration
      await redisClient.expire(key, windowSeconds);
    }
    
    return {
      allowed: current <= limit,
      current,
      limit,
      remaining: Math.max(0, limit - current),
      resetTime: Date.now() + (windowSeconds * 1000)
    };
  } catch (error) {
    console.error('Error checking rate limit:', error);
    // If Redis fails, allow the request
    return {
      allowed: true,
      current: 0,
      limit,
      remaining: limit,
      resetTime: Date.now() + (windowSeconds * 1000)
    };
  }
}
