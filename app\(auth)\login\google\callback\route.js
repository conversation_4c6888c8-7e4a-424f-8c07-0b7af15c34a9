import { NextResponse } from 'next/server'
const { OAuth2Client } = require('google-auth-library');
import { sendLoginGoogle } from '@/lib/http';
import { settingCookies } from '@/lib/utils';
export async function GET(request) {
    let code = request.nextUrl.searchParams.get('code')
    const client = new OAuth2Client(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET,
        process.env.GOOGLE_REDIRECT_URI
    );
    try {
        const { tokens } = await client.getToken(code);
        client.setCredentials(tokens);

        const ticket = await client.verifyIdToken({
            idToken: tokens.id_token,
            audience: process.env.GOOGLE_CLIENT_ID,
        });

        const payload = ticket.getPayload();
        const response = await sendLoginGoogle(payload)
        const res = NextResponse.redirect(new URL(process.env.REALM))
        res.cookies.set('token', response.accessToken, settingCookies)
        return res
    } catch (error) {
        return NextResponse.redirect(new URL(`${process.env.REALM}/login`, request.url))
    }

}