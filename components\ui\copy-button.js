'use client'

import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { ClipboardCopy, Check } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { ToastAction } from "@/components/ui/toast"


export default function CopyButton({ text, className = '' }) {
  const [isCopied, setIsCopied] = useState(false)
  const { toast } = useToast()

  const handleCopy = async () => {
    if (text) {
      try {
        await navigator.clipboard.writeText(text)
        setIsCopied(true)
        toast({
          title: "Đã sao chép!",
          description: "Văn bản đã được sao chép vào clipboard",
          action:(
            <ToastAction altText="Goto schedule to undo">Undo</ToastAction>
          )
        })
        setTimeout(() => setIsCopied(false), 2000)
      } catch (err) {
        toast({
          title: "Không thể sao chép",
          description: "<PERSON><PERSON> lòng thử lại",
          variant: "destructive",
        })
      }
    }
  }

  return (
    <Button 
      onClick={handleCopy} 
      variant="link" 
      size="icon"
      className={className}
    >
      {isCopied ? (
        <Check className="h-4 w-4" />
      ) : (
        <ClipboardCopy className="h-4 w-4" />
      )}
    </Button>
  )
}