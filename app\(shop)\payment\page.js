import OrderInfo from "@/components/features/orderinfo/OrderInfo";
import { verifyToken } from "@/lib/jwt";
export const metadata = {
    title: 'Thanh toán cho đơn hàng ',
    description: 'Web bán coin csgoempire.com tự động',
    keywords: "csgoempire csgo csgoempirevn empirevn empirevn.store, item csgo, skin csgo"
}
export default function Page({ searchParams }) {
    let token = searchParams?.token

    if (!token) {
        return (<div>Đã có lỗi xảy ra</div>)
    }
    const orderInfo = verifyToken(token)

    if (!orderInfo) {
        return <div>Invalid token or session expired.</div>;
    }
    if (orderInfo?.steamAvatar) orderInfo.coin = orderInfo.coin / 100
    const { steamId, steamName, coin, steamLevel, steamAvatar, rate, content, bank, orderId } = orderInfo

    const steamInfo = { steamId, steamName, coin, steamLevel, steamAvatar }
    if (coin < 10) {
        steamInfo.money = Math.ceil(coin * 1000 * rate)
    } else steamInfo.money = Math.ceil(coin * rate) * 1000
    const bankInfo = { ...bank, content, money: steamInfo.money }

    return (
        <>
            <OrderInfo
                steamInfo={steamInfo}
                bankInfo={bankInfo}
                orderId={orderId}
                data={{
                    bank: bankInfo,
                    amount: steamInfo.money,
                    content: content,
                    orderId: orderId
                }}
            />
        </>
    )
}

