"use client";
import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Trash2, RefreshCw, ExternalLink, CheckCircle, XCircle, Clock } from "lucide-react";
import Pagination from '@/components/ui/Pagination';

export default function TransactionHistory() {
    const [transactions, setTransactions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [lastUpdate, setLastUpdate] = useState(null);

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(50);
    const [pagination, setPagination] = useState({
        page: 1,
        limit: 50,
        total: 0,
        totalPages: 0
    });

    // Fetch transaction history with pagination
    const fetchTransactions = async (page = currentPage, limit = itemsPerPage) => {
        setLoading(true);
        setError(null);

        try {
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString()
            });

            const response = await fetch(`/api/admin/coin-monitor/transactions?${params}`);
            const data = await response.json();

            if (data.success) {
                setTransactions(data.data);
                setPagination(data.pagination);
                setCurrentPage(data.pagination.page);
                setLastUpdate(new Date());
            } else {
                setError(data.error);
            }
        } catch (err) {
            setError('Failed to fetch transaction history');
            console.error('Error fetching transactions:', err);
        } finally {
            setLoading(false);
        }
    };

    // Cleanup old transactions
    const cleanupOldTransactions = async () => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await fetch('/api/admin/coin-monitor/transactions', {
                method: 'DELETE'
            });
            
            const data = await response.json();
            
            if (data.success) {
                alert(`Successfully deleted ${data.deletedRows} old transactions`);
                fetchTransactions(); // Refresh the list
            } else {
                setError(data.error);
            }
        } catch (err) {
            setError('Failed to cleanup old transactions');
            console.error('Error cleaning up transactions:', err);
        } finally {
            setLoading(false);
        }
    };

    // Handle pagination changes
    const handlePageChange = (page) => {
        setCurrentPage(page);
        fetchTransactions(page, itemsPerPage);
    };

    const handleItemsPerPageChange = (newItemsPerPage) => {
        setItemsPerPage(newItemsPerPage);
        setCurrentPage(1); // Reset to first page
        fetchTransactions(1, newItemsPerPage);
    };

    // Auto-refresh every 30 seconds
    useEffect(() => {
        fetchTransactions();
        const interval = setInterval(() => {
            fetchTransactions(currentPage, itemsPerPage);
        }, 30000);
        return () => clearInterval(interval);
    }, [currentPage, itemsPerPage]);

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString('vi-VN');
    };

    const formatCoin = (amount) => {
        return parseFloat(amount).toFixed(2);
    };

    const formatVND = (amount) => {
        return amount ? amount.toLocaleString('vi-VN') : 'N/A';
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Transaction History</h2>
                    <p className="text-gray-600 dark:text-gray-400">
                        Lịch sử các giao dịch đã được xử lý bởi worker
                    </p>
                </div>
                
                <div className="flex gap-2">
                    <Button
                        variant="outline"
                        onClick={() => fetchTransactions()}
                        disabled={loading}
                        className="flex items-center gap-2"
                    >
                        <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                    
                    <Button
                        variant="destructive"
                        onClick={cleanupOldTransactions}
                        disabled={loading}
                        className="flex items-center gap-2"
                    >
                        <Trash2 className="h-4 w-4" />
                        Cleanup Old
                    </Button>
                </div>
            </div>

            {/* Error Alert */}
            {error && (
                <Alert variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="text-2xl font-bold text-blue-600">
                            {transactions.length}
                        </div>
                        <p className="text-sm text-gray-600">Total Transactions</p>
                    </CardContent>
                </Card>
                
                <Card>
                    <CardContent className="p-4">
                        <div className="text-2xl font-bold text-green-600">
                            {transactions.filter(t => t.matched_order_id).length}
                        </div>
                        <p className="text-sm text-gray-600">Matched Orders</p>
                    </CardContent>
                </Card>
                
                <Card>
                    <CardContent className="p-4">
                        <div className="text-2xl font-bold text-purple-600">
                            {transactions.filter(t => t.notification_sent).length}
                        </div>
                        <p className="text-sm text-gray-600">Notifications Sent</p>
                    </CardContent>
                </Card>
                
                <Card>
                    <CardContent className="p-4">
                        <div className="text-2xl font-bold text-orange-600">
                            {transactions.reduce((sum, t) => sum + parseFloat(t.coin_amount || 0), 0).toFixed(2)}
                        </div>
                        <p className="text-sm text-gray-600">Total Coins</p>
                    </CardContent>
                </Card>
            </div>

            {/* Transaction Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Recent Transactions ({pagination.total})</CardTitle>
                    <CardDescription>
                        {lastUpdate && `Last updated: ${lastUpdate.toLocaleTimeString()}`}
                    </CardDescription>
                </CardHeader>
                
                <CardContent>
                    {loading && transactions.length === 0 ? (
                        <div className="text-center py-8">
                            <RefreshCw className="h-8 w-8 mx-auto mb-2 text-gray-400 animate-spin" />
                            <p className="text-gray-500">Loading transactions...</p>
                        </div>
                    ) : transactions.length === 0 ? (
                        <div className="text-center py-8">
                            <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                            <p className="text-gray-500">No transactions found</p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Transaction ID</TableHead>
                                        <TableHead>Steam ID</TableHead>
                                        <TableHead>Coin Amount</TableHead>
                                        <TableHead>Order Match</TableHead>
                                        <TableHead>VND Amount</TableHead>
                                        <TableHead>Notification</TableHead>
                                        <TableHead>Date</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {transactions.map((transaction) => (
                                        <TableRow key={transaction.id}>
                                            <TableCell className="font-mono text-sm">
                                                {transaction.transaction_id}
                                            </TableCell>
                                            
                                            <TableCell className="font-mono text-sm">
                                                {transaction.steam_id ? (
                                                    <span className="text-blue-600">
                                                        {transaction.steam_id}
                                                    </span>
                                                ) : (
                                                    <span className="text-gray-400">N/A</span>
                                                )}
                                            </TableCell>
                                            
                                            <TableCell>
                                                <span className="font-semibold text-green-600">
                                                    {formatCoin(transaction.coin_amount)}
                                                </span>
                                            </TableCell>
                                            
                                            <TableCell>
                                                {transaction.matched_order_id ? (
                                                    <div className="space-y-1">
                                                        <Badge variant="default" className="text-xs">
                                                            {transaction.matched_order_id}
                                                        </Badge>
                                                        <div className="text-xs text-gray-500">
                                                            {transaction.order_status}
                                                        </div>
                                                    </div>
                                                ) : (
                                                    <Badge variant="secondary" className="text-xs">
                                                        No Match
                                                    </Badge>
                                                )}
                                            </TableCell>
                                            
                                            <TableCell>
                                                {transaction.vnd_amount ? (
                                                    <span className="font-semibold">
                                                        {formatVND(transaction.vnd_amount)} VND
                                                    </span>
                                                ) : (
                                                    <span className="text-gray-400">N/A</span>
                                                )}
                                            </TableCell>
                                            
                                            <TableCell>
                                                {transaction.notification_sent ? (
                                                    <Badge variant="default" className="flex items-center gap-1 w-fit">
                                                        <CheckCircle className="h-3 w-3" />
                                                        Sent
                                                    </Badge>
                                                ) : (
                                                    <Badge variant="destructive" className="flex items-center gap-1 w-fit">
                                                        <XCircle className="h-3 w-3" />
                                                        Failed
                                                    </Badge>
                                                )}
                                            </TableCell>
                                            
                                            <TableCell className="text-sm">
                                                {formatDate(transaction.processed_at)}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>

                            {/* Pagination */}
                            <Pagination
                                currentPage={pagination.page}
                                totalPages={pagination.totalPages}
                                totalItems={pagination.total}
                                itemsPerPage={itemsPerPage}
                                onPageChange={handlePageChange}
                                onItemsPerPageChange={handleItemsPerPageChange}
                                className="mt-4"
                            />
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
