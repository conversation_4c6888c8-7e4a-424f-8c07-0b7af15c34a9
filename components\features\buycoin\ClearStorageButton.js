"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Trash2 } from "lucide-react";

export default function ClearStorageButton({ onClear, hasStoredData }) {
  if (!hasStoredData) return null;

  const handleClear = () => {
    if (confirm('Bạn có muốn xóa thông tin đã lưu không?')) {
      onClear();
    }
  };

  return (
    <div className="flex justify-end">
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={handleClear}
        className="text-gray-500 hover:text-red-600 hover:border-red-300"
      >
        <Trash2 className="h-2 w-2 mr-1" />
        <PERSON><PERSON><PERSON> tất cả dữ liệu đã lưu
      </Button>
    </div>
  );
}
