"use client";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { ArrowLeftRight, Coins, DollarSign } from "lucide-react";

export default function CurrencyExchange({
  coin,
  vnd,
  rate,
  onCoinChange,
  onVndChange,
  register,
  errors,
  state
}) {
  return (
    <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-3 md:p-6 rounded-xl border border-blue-200 dark:border-blue-800">
      <div className="grid grid-cols-1 md:grid-cols-[1fr_auto_1fr] gap-2 md:gap-4 items-start md:items-center">
        {/* Coin Input */}
        <div className="w-full">
          <Label htmlFor="coin" className="flex items-center space-x-2 text-sm md:text-base font-medium mb-1.5 md:mb-2">
            <Coins className="h-4 w-4 text-orange-600" />
            <span>Số Coin</span>
          </Label>
          <div className="relative">
            <Input
              type="text"
              {...register("coin")}
              value={coin}
              onChange={onCoinChange}
              placeholder="0.00"
              className="h-11 md:h-12 pl-4 text-base md:text-lg font-semibold border-2 border-orange-200 dark:border-orange-600 focus:border-orange-500 transition-all duration-200"
              required
            />
            <Badge className="absolute right-3 top-2.5 md:top-3 text-xs bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
              COIN
            </Badge>
          </div>
          {errors.coin && (
            <p className="text-xs md:text-sm text-red-500 mt-1.5 md:mt-2 flex items-center space-x-1">
              <span>⚠️</span>
              <span>{errors.coin.message}</span>
            </p>
          )}
          {state?.errors?.coin && (
            <p className="text-xs md:text-sm text-red-500 mt-1.5 md:mt-2 flex items-center space-x-1">
              <span>⚠️</span>
              <span>{state.errors.coin[0]}</span>
            </p>
          )}
        </div>

        {/* Exchange Arrow */}
        <div className="flex items-center justify-center py-1 md:py-0 md:px-2 -my-1 md:my-0">
          <div className="bg-white dark:bg-slate-700 rounded-full p-1.5 md:p-3 shadow-md md:shadow-lg border border-blue-200 dark:border-blue-600 transform md:translate-y-4">
            <ArrowLeftRight className="h-4 w-4 md:h-6 md:w-6 text-blue-600 animate-pulse rotate-90 md:rotate-0" />
          </div>
        </div>

        {/* VND Input */}
        <div className="w-full">
          <Label htmlFor="vnd" className="flex items-center space-x-2 text-sm md:text-base font-medium mb-1.5 md:mb-2">
            <DollarSign className="h-4 w-4 text-green-600" />
            <span>Số Tiền (VND)</span>
          </Label>
          <div className="relative">
            <Input
              type="text"
              {...register("amount")}
              value={vnd}
              onChange={onVndChange}
              placeholder="0"
              className="h-11 md:h-12 pl-4 text-base md:text-lg font-semibold border-2 border-green-200 dark:border-green-600 focus:border-green-500 transition-all duration-200"
              required
            />
            <Badge className="absolute right-3 top-2.5 md:top-3 text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              VND
            </Badge>
          </div>
          {errors.amount && (
            <p className="text-xs md:text-sm text-red-500 mt-1.5 md:mt-2 flex items-center space-x-1">
              <span>⚠️</span>
              <span>{errors.amount.message}</span>
            </p>
          )}
          {state?.errors?.amount && (
            <p className="text-xs md:text-sm text-red-500 mt-1.5 md:mt-2 flex items-center space-x-1">
              <span>⚠️</span>
              <span>{state.errors.amount[0]}</span>
            </p>
          )}
        </div>
      </div>

      {/* Exchange Rate Info */}
      <div className="mt-2 md:mt-4 text-center">
        <p className="text-xs md:text-sm text-gray-600 dark:text-gray-400">
          💡 Tỷ giá hiện tại: <span className="font-semibold text-blue-600 dark:text-blue-400">1 Coin = {(rate * 1000).toLocaleString()} VND</span>
        </p>
      </div>
    </div>
  );
}
