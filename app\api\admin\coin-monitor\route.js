import { NextResponse } from 'next/server';
import { testTelegramConnection, validateTelegramConfig } from '@/service/telegram-service';
import { validateApiConfig } from '@/service/csgoempire-service';
import coinMonitorWorker from '@/worker/coin-worker';


/**
 * GET /api/admin/coin-monitor
 * Get worker status and statistics
 */
export async function GET() {
    try {
        const status = coinMonitorWorker.getStatus();

        return NextResponse.json({
            success: true,
            status: status
        });

    } catch (error) {
        console.error('Error getting worker status:', error);

        return NextResponse.json({
            success: false,
            error: error.message
        }, { status: 500 });
    }
}

/**
 * POST /api/admin/coin-monitor
 * Control worker (start/stop/test)
 */
export async function POST(request) {
    try {
        const body = await request.json();
        const { action } = body;
        
        if (!action) {
            return NextResponse.json({
                success: false,
                error: 'Action is required'
            }, { status: 400 });
        }
        
        let result;
        
        switch (action.toLowerCase()) {
            case 'start':
                result = await coinMonitorWorker.start();
                break;
                
            case 'stop':
                result = await coinMonitorWorker.stop();
                break;
                
            case 'restart':
                // Stop first, then start
                await coinMonitorWorker.stop();
                // Wait a moment before starting
                await new Promise(resolve => setTimeout(resolve, 1000));
                result = await coinMonitorWorker.start();
                break;
                
            case 'test-telegram':
                result = await testTelegramConnection;
                break;

            case 'validate-config':
                const apiValidation = await validateApiConfig();
                const telegramValidation = await validateTelegramConfig();

                result = {
                    success: apiValidation.isValid && telegramValidation.isValid,
                    data: {
                        csgoempire: apiValidation,
                        telegram: telegramValidation
                    }
                };
                break;

            default:
                return NextResponse.json({
                    success: false,
                    error: `Unknown action: ${action}. Valid actions: start, stop, restart, test-telegram, validate-config`
                }, { status: 400 });
        }
        
        return NextResponse.json(result);
        
    } catch (error) {
        console.error('Error controlling worker:', error);
        
        return NextResponse.json({
            success: false,
            error: error.message
        }, { status: 500 });
    }
}
