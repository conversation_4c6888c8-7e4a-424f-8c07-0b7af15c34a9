import { NextResponse } from 'next/server';
import { deleteCache, getCache } from '@/lib/redis';
import { checkAdminAuth } from '@/lib/utils';

// DELETE /api/bank-account-lookup/cache - Clear specific cache entry
export async function DELETE(request) {
    try {
        // Check admin authentication
        const { isAdmin } = await checkAdminAuth(request.cookies);
        if (!isAdmin) {
            return NextResponse.json(
                { error: 'Unauthorized. Admin access required.' },
                { status: 403 }
            );
        }

        const { searchParams } = new URL(request.url);
        const bankBin = searchParams.get('bankBin');
        const accountNumber = searchParams.get('accountNumber');

        if (!bankBin || !accountNumber) {
            return NextResponse.json(
                { error: 'Missing bankBin or accountNumber parameters' },
                { status: 400 }
            );
        }

        const cacheKey = `bank_lookup:${bankBin}:${accountNumber}`;
        const deleted = await deleteCache(cacheKey);

        return NextResponse.json({
            success: true,
            message: deleted ? 'Cache entry deleted successfully' : 'Cache entry not found',
            cacheKey
        });

    } catch (error) {
        console.error('Error deleting cache:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// GET /api/bank-account-lookup/cache - Get cache entry
export async function GET(request) {
    try {
        // Check admin authentication
        const { isAdmin } = await checkAdminAuth(request.cookies);
        if (!isAdmin) {
            return NextResponse.json(
                { error: 'Unauthorized. Admin access required.' },
                { status: 403 }
            );
        }

        const { searchParams } = new URL(request.url);
        const bankBin = searchParams.get('bankBin');
        const accountNumber = searchParams.get('accountNumber');

        if (!bankBin || !accountNumber) {
            return NextResponse.json(
                { error: 'Missing bankBin or accountNumber parameters' },
                { status: 400 }
            );
        }

        const cacheKey = `bank_lookup:${bankBin}:${accountNumber}`;
        const cachedData = await getCache(cacheKey);

        return NextResponse.json({
            success: true,
            cacheKey,
            cached: !!cachedData,
            data: cachedData
        });

    } catch (error) {
        console.error('Error getting cache:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}
