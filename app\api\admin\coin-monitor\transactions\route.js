import { cleanupOldTransactions, getRecentProcessedTransactions } from '@/service/transaction-service';
import { NextResponse } from 'next/server';

/**
 * GET /api/admin/coin-monitor/transactions
 * Get recent processed transactions with pagination
 */
export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const page = Math.max(1, parseInt(searchParams.get('page')) || 1);
        const limit = Math.min(200, Math.max(1, parseInt(searchParams.get('limit')) || 50));

        // Validate numeric parameters
        if (isNaN(page) || isNaN(limit)) {
            return NextResponse.json({
                success: false,
                error: 'Invalid pagination parameters'
            }, { status: 400 });
        }

        const result = await getRecentProcessedTransactions(limit, page);

        return NextResponse.json(result);

    } catch (error) {
        console.error('Error fetching processed transactions:', error);

        return NextResponse.json({
            success: false,
            error: error.message
        }, { status: 500 });
    }
}

/**
 * DELETE /api/admin/coin-monitor/transactions
 * Cleanup old transactions (older than 30 days)
 */
export async function DELETE() {
    try {
        const result = await cleanupOldTransactions();
        
        return NextResponse.json(result);
        
    } catch (error) {
        console.error('Error cleaning up old transactions:', error);
        
        return NextResponse.json({
            success: false,
            error: error.message
        }, { status: 500 });
    }
}
