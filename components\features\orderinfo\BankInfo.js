import { Button } from "@/components/ui/button";
import { Copy, Check, Building2, CreditCard, User, DollarSign, MessageSquare } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

export default function BankInfo({ data }) {
  const [copiedItems, setCopiedItems] = useState({});
  const { toast } = useToast();

  // Kiểm tra và xử lý data
  if (!data || !data.bank) {
    return (
      <div className="text-center text-red-300">
        <p>Không thể tải thông tin ngân hàng</p>
      </div>
    );
  }

  const { bankName, bankNo, accountName } = data.bank;
  const money = data.amount || 0;

  const copyToClipboard = async (text, itemName) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedItems(prev => ({ ...prev, [itemName]: true }));

      if (toast) {
        toast({
          title: "Đã sao chép!",
          description: `${itemName} đã được sao chép vào clipboard`,
          duration: 2000,
        });
      }

      setTimeout(() => {
        setCopiedItems(prev => ({ ...prev, [itemName]: false }));
      }, 2000);
    } catch (err) {
      if (toast) {
        toast({
          title: "Lỗi sao chép",
          description: "Không thể sao chép. Vui lòng thử lại.",
          variant: "destructive",
        });
      }
    }
  };

  const bankInfoItems = [
    {
      icon: Building2,
      label: "Ngân hàng",
      value: bankName,
      copyable: false,
    },
    {
      icon: CreditCard,
      label: "Số tài khoản",
      value: bankNo,
      copyable: true,
      copyKey: "bankNo",
    },
    {
      icon: User,
      label: "Chủ tài khoản",
      value: accountName,
      copyable: false,
    },
    {
      icon: DollarSign,
      label: "Số tiền cần thanh toán",
      value: Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(money),
      copyable: true,
      copyKey: "amount",
      copyValue: money.toString(),
    },
  ];

  return (
    <div className="space-y-2">
      {bankInfoItems.map((item, index) => {
        const IconComponent = item.icon;
        const isCopied = copiedItems[item.copyKey];

        return (
          <div key={index} className="group">
            {item.copyable ? (
              <div className="flex items-center justify-between bg-blue-50/60 dark:bg-white/10 rounded-md p-2 border border-blue-200/40 dark:border-white/20 hover:bg-blue-100/60 dark:hover:bg-white/15 transition-all duration-200">
                <div className="flex items-center space-x-2 flex-1 mr-2">
                  <IconComponent className="h-3 w-3 text-blue-600 dark:text-white opacity-90 flex-shrink-0" />
                  <span className="text-xs font-medium text-gray-700 dark:text-white opacity-90 flex-shrink-0">{item.label}:</span>
                  <span className="text-sm font-semibold text-gray-800 dark:text-white break-all">{item.value}</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 hover:bg-blue-200/50 dark:hover:bg-white/20 transition-all duration-200 flex-shrink-0"
                  onClick={() => copyToClipboard(item.copyValue || item.value, item.label)}
                  aria-label={`Sao chép ${item.label}`}
                >
                  {isCopied ? (
                    <Check className="h-3 w-3 text-green-600 dark:text-green-300" />
                  ) : (
                    <Copy className="h-3 w-3 text-blue-600 dark:text-white" />
                  )}
                </Button>
              </div>
            ) : (
              <div className="flex items-center space-x-2 bg-gray-50/60 dark:bg-white/5 rounded-md p-2 border border-gray-200/40 dark:border-white/10">
                <IconComponent className="h-3 w-3 text-blue-600 dark:text-white opacity-90 flex-shrink-0" />
                <span className="text-xs font-medium text-gray-700 dark:text-white opacity-90 flex-shrink-0">{item.label}:</span>
                <span className="text-sm font-semibold text-gray-800 dark:text-white">{item.value}</span>
              </div>
            )}
          </div>
        );
      })}

      {/* Nội dung chuyển khoản - Compact */}
      <div className="mt-2 pt-2 border-t border-gray-300/30 dark:border-white/10">
        <div className="bg-gradient-to-r from-orange-100/80 to-red-100/80 dark:from-amber-400/25 dark:to-orange-400/25 rounded-md p-2 border border-orange-200/60 dark:border-amber-300/40">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 flex-1 mr-2">
              <MessageSquare className="h-3 w-3 text-orange-600 dark:text-amber-200 flex-shrink-0" />
              <span className="text-xs font-medium text-orange-700 dark:text-amber-100 flex-shrink-0">Nội dung:</span>
              <span className="text-sm font-bold tracking-wide text-orange-800 dark:text-amber-100 break-all">
                {data.content || 'N/A'}
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 hover:bg-orange-200/50 dark:hover:bg-white/20 transition-all duration-200 flex-shrink-0"
              onClick={() => copyToClipboard(data.content || '', "Nội dung chuyển khoản")}
              aria-label="Sao chép nội dung chuyển khoản"
            >
              {copiedItems["content"] ? (
                <Check className="h-3 w-3 text-green-600 dark:text-green-300" />
              ) : (
                <Copy className="h-3 w-3 text-orange-600 dark:text-amber-200" />
              )}
            </Button>
          </div>
          <p className="text-xs text-orange-600/80 dark:text-amber-200/80 mt-1 ml-5">
            ⚠️ Nhập chính xác nội dung này
          </p>
        </div>
      </div>
    </div>
  );
}
