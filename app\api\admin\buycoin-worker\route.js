import buyCoinOrderWorker from '@/worker/buycoin-worker';
import { NextResponse } from 'next/server';

// GET - Get worker status
export async function GET() {
    try {
        const status = buyCoinOrderWorker.getStatus();
        
        // Get expired orders stats
        const expiredStats = await buyCoinOrderWorker.fetchExpiredOrdersStats();
        
        return NextResponse.json({
            success: true,
            status,
            expiredStats: expiredStats.success ? expiredStats.data : []
        });
        
    } catch (error) {
        console.error('Error getting worker status:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to get worker status'
        }, { status: 500 });
    }
}

// POST - Start/Stop worker
export async function POST(request) {
    try {
        const body = await request.json();
        const { action } = body;
        
        let result;
        
        switch (action) {
            case 'start':
                result = await buyCoinOrderWorker.start();
                break;
                
            case 'stop':
                result = await buyCoinOrderWorker.stop();
                break;
                
            case 'restart':
                // Stop first, then start
                await buyCoinOrderWorker.stop();
                // Wait a moment before starting
                await new Promise(resolve => setTimeout(resolve, 1000));
                result = await buyCoinOrderWorker.start();
                break;
                
            default:
                return NextResponse.json({
                    success: false,
                    error: 'Invalid action. Use: start, stop, or restart'
                }, { status: 400 });
        }
        
        return NextResponse.json(result);
        
    } catch (error) {
        console.error('Error controlling worker:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to control worker'
        }, { status: 500 });
    }
}
