import { NextResponse } from 'next/server';
import { checkAuth } from '@/lib/utils';
import { cookies } from 'next/headers';

export async function GET() {
    const cookieStore = cookies();
const { isLoggedIn, userInfo } = await checkAuth(cookieStore);
    try {
        if (!isLoggedIn || !userInfo) {
            return NextResponse.json({
                success: false,
                error: 'Not authenticated'
            }, { status: 401 });
        }

        return NextResponse.json({
            success: true,
            user: userInfo
        });

    } catch (error) {
        console.error('Error getting user info:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to get user info'
        }, { status: 500 });
    }
}
