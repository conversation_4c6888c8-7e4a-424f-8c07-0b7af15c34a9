export const metadata = {
    title: 'Thông tin tài khoản',
    description: 'Web bán coin csgoempire.com tự động',
}
import Sidebar from '@/components/features/dashboard/Sidebar'
import { fetchUserInfo } from '@/lib/http'
export default async function DashboardLayout({ children }) {
    const userInfo = await fetchUserInfo()
    return (
        <div className=" max-w-8xl mx-auto px-2 pb-6 sm:px-6 md:px-8 xl:px-12">
            <div className="relative">
                <div className="mt-4 -mb-3">
                    <div className="not-prose relative overflow-hidden  antialiased">
                        <div className='flex flex-col md:flex-row'>
                            <Sidebar userInfo={userInfo} />
                            <main className="flex-1 md:p-6 overflow-y-auto">
                                {children}
                            </main>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}