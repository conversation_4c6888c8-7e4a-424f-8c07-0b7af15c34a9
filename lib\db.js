import mysql from 'mysql2/promise';

let pool;

function createPool() {
  return mysql.createPool({
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'Minhhoang93@',
    database: process.env.DB_NAME || 'empirevn',
    waitForConnections: true,
    connectionLimit: Number(process.env.DB_POOL_SIZE || 10),
    queueLimit: 0,
    charset: process.env.DB_CHARSET || 'utf8mb4_unicode_ci',
    timezone: process.env.DB_TIMEZONE || 'Z',
    namedPlaceholders: true,
  });
}
export function getPool() {
  if (pool) return pool;

  // Lưu vào globalThis để tránh tạo nhiều pool khi hot-reload (Next.js dev mode)
  if (!globalThis.__MYSQL_POOL__) {
    globalThis.__MYSQL_POOL__ = createPool();
  }
  pool = globalThis.__MYSQL_POOL__;
  return pool;
}


export async function query(sql, params = []) {
  return await getPool().execute(sql, params);
  
}

export async function execute(sql, params = []) {
  const [rows, fields] = await getPool().execute(sql, params);
  return [rows, fields];
}
export async function closePool() {
  if (pool) {
    await pool.end();
    pool = null;
    globalThis.__MYSQL_POOL__ = null;
  }
}
export async function withTransaction(work) {
  const conn = await getPool().getConnection();
  try {
    await conn.beginTransaction();
    const result = await work(conn);
    await conn.commit();
    return result;
  } catch (err) {
    await conn.rollback();
    throw err;
  } finally {
    conn.release();
  }
}

