"use server";
'server-only'
import { loginChema } from "@/validations/auth.schema";
import { redirect } from 'next/navigation';
import { loginUser } from "@/lib/http";
import { cookies } from 'next/headers'
import { settingCookies } from "@/lib/utils";
export async function login(prevState, data) {
    const parseResult = loginChema.safeParse(Object.fromEntries(data.entries()))
    if (!parseResult.success) {
        return {
            ...prevState,
            fieldErrors: parseResult.error.flatten().fieldErrors,
            serverError: null,
            message: "Missing Fields. Failed to Login.",
        };
    }
    const user = {
        username: data.get("username"),
        password: data.get("password"),
    }
    const response = await loginUser(user)
    if (!response) {
        return {
            ...prevState,
            fieldErrors: {},
            serverError: true,
            message: "<PERSON><PERSON> có lỗi xảy ra xin vui lòng thử lại",
        };
    }

    if (response?.error == "Username or password incorrect") {
        return {
            ...prevState,
            fieldErrors: {},
            serverError: true,
            message: "<PERSON><PERSON><PERSON> khoản hoặc mật khẩu không chính xác",
        };
    }
    if (response.error) return {
        ...prevState,
        fieldErrors: {},
        serverError: true,
        message: response.error,
    };
    cookies().set("token", response.metadata, settingCookies);
    redirect("/");
}
export async function logout(){
    cookies().set('token', '')
    redirect("/")
}