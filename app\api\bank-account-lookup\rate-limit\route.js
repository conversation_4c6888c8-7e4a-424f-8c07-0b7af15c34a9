import { NextResponse } from 'next/server';
import { checkRateLimit } from '@/lib/redis';
import { headers } from 'next/headers';
import { checkAdminAuth } from '@/lib/utils';

// GET /api/bank-account-lookup/rate-limit - Check current rate limit status
export async function GET(request) {
    try {
        // Check admin authentication
        const { isAdmin } = await checkAdminAuth(request.cookies);
        if (!isAdmin) {
            return NextResponse.json(
                { error: 'Unauthorized. Admin access required.' },
                { status: 403 }
            );
        }

        // Get client IP
        const headersList = headers();
        const clientIP = headersList.get('x-forwarded-for') || 
                        headersList.get('x-real-ip') || 
                        'unknown';

        const rateLimitKey = `bank_lookup_rate_limit:${clientIP}`;
        
        // Check current rate limit status without incrementing
        const rateLimit = await checkRateLimit(rateLimitKey, 5, 60);

        return NextResponse.json({
            success: true,
            clientIP,
            rateLimitKey,
            rateLimit: {
                allowed: rateLimit.allowed,
                current: rateLimit.current,
                limit: rateLimit.limit,
                remaining: rateLimit.remaining,
                resetTime: rateLimit.resetTime,
                resetTimeFormatted: new Date(rateLimit.resetTime).toISOString()
            }
        });

    } catch (error) {
        console.error('Error checking rate limit:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}
