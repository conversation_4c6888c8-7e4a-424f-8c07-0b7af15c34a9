{"name": "client", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "bank-checker": "node background-bank-checker.js"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-form": "^0.1.0", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "base64-arraybuffer": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookie": "^1.0.1", "framer-motion": "^11.18.2", "google-auth-library": "^9.14.2", "jose": "^5.9.4", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.452.0", "mysql2": "^3.14.2", "mysql2-promise": "^0.1.4", "next": "14.2.15", "next-themes": "^0.3.0", "node-cron": "^4.2.1", "node-steam-openid": "^1.2.3", "openid": "^2.0.12", "react": "^18", "react-dom": "^18", "react-twc": "^1.4.2", "recharts": "^2.13.0", "redis": "^5.6.1", "server-only": "^0.0.1", "steam-signin": "^1.0.4", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zca-js": "^2.0.0-beta.25", "zod": "^3.23.8"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.2.15", "postcss": "^8", "tailwindcss": "^3.4.1"}}