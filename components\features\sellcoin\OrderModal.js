"use client";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import OrderInfo from "../orderinfo/OrderInfo";

export default function OrderModal({ isOpen, onClose, state }) {
  // Tạo steamInfo từ state.data nếu có steamAvatar và steamName
  const createSteamInfo = (data) => {
    if (!data || !data.steamAvatar || !data.steamName) return null;

    return {
      steamId: data.steamId,
      steamName: data.steamName,
      steamAvatar: data.steamAvatar,
      steamLevel: data.steamLevel,
      coin: data.coin,
    };
  };

  const steamInfo = createSteamInfo(state?.data);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      {state?.success && (
        <DialogContent className="max-w-2xl w-full max-h-[90vh] p-0 border-0 overflow-y-auto">
          <OrderInfo
            data={state?.data}
            steamInfo={steamInfo}
          />
        </DialogContent>
      )}
    </Dialog>
  );
}
