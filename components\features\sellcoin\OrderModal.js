"use client";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import OrderInfo from "../orderinfo/OrderInfo";

export default function OrderModal({ isOpen, onClose, state }) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      {state?.success && (
        <DialogContent className="max-w-2xl w-full max-h-[90vh] p-0 border-0 overflow-y-auto">
          <OrderInfo data={state?.data} />
        </DialogContent>
      )}
    </Dialog>
  );
}
