"use client";
import { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ShoppingCart, TrendingDown, Headphones } from "lucide-react";
import SellCoinTab from "../sellcoin/SellCoinTab";
import BuyCoinTab from "../buycoin/BuyCoinTab";
import SupportTab from "../support/SupportTab";

export default function OrderTabs({ banks, rate, availableCoins, steamId, facebook }) {
  const [activeTab, setActiveTab] = useState("buy");

  const handleTabChange = (value) => {
    setActiveTab(value);
    // Emit custom event to notify CoinDisplay
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('orderTabChange', {
        detail: { tab: value }
      }));
    }
  };

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
      <TabsList className="grid w-full grid-cols-3 mb-4 md:mb-6">
        <TabsTrigger value="buy" className="flex items-center space-x-2">
          <ShoppingCart className="h-4 w-4" />
          <span>Mua Coin</span>
        </TabsTrigger>
        <TabsTrigger value="sell" className="flex items-center space-x-2">
          <TrendingDown className="h-4 w-4" />
          <span>Thu Mua Coin</span>
        </TabsTrigger>
        <TabsTrigger value="support" className="flex items-center space-x-2">
          <Headphones className="h-4 w-4" />
          <span>Hỗ Trợ</span>
        </TabsTrigger>
      </TabsList>

      {/* Tab Mua Coin */}
      <TabsContent value="buy">
        <SellCoinTab
          banks={banks}
          rate={rate}
          availableCoins={availableCoins}
          steamId={steamId}
        />
      </TabsContent>

      {/* Tab Thu Mua Coin */}
      <TabsContent value="sell">
        <BuyCoinTab facebook={facebook} buyRate={rate}/>
      </TabsContent>

      {/* Tab Hỗ Trợ */}
      <TabsContent value="support">
        <SupportTab facebook={facebook} />
      </TabsContent>
    </Tabs>
  );
}
