import React from "react";
import Link from "next/link";
import { Facebook, Phone, Clock, Shield, Award } from "lucide-react";
import { Logo } from "@/components/layout/header/Logo";

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const yearRange = currentYear > 2024 ? `2024-${currentYear}` : '2024';

  return (
    <footer className="relative bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 text-foreground border-t border-border overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 opacity-40 dark:opacity-60">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100/40 via-purple-100/25 to-indigo-100/40 dark:from-blue-950/30 dark:via-purple-950/20 dark:to-indigo-950/30"></div>
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
        <div className="absolute inset-0 dark:block hidden" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}></div>
      </div>

      {/* Main footer content */}
      <div className="relative z-10">
        {/* Top section - Hidden on mobile */}
        

        {/* Bottom section */}
        <div className="py-6 bg-gradient-to-r from-slate-100/50 to-blue-100/30 dark:from-slate-900/50 dark:to-slate-800/30">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
              <div className="text-center md:text-left">
                <p className="text-muted-foreground dark:text-slate-400 text-sm">
                  © {yearRange} <Link href="/" className="text-primary hover:text-primary/80 transition-colors duration-300 font-medium">EmpireVN</Link>. All Rights Reserved.
                </p>
                <p className="text-foreground/80 dark:text-slate-200 text-sm mt-1 hidden md:block font-medium">
                  Thiết kế và phát triển bởi EmpireVN Team
                </p>
              </div>

              <div className="hidden md:flex items-center space-x-4 text-sm text-foreground/90 dark:text-slate-200 font-medium">
                <span className="flex items-center space-x-1">
                  <Shield className="h-3 w-3 text-green-600 dark:text-green-400" />
                  <span>Bảo mật SSL</span>
                </span>
                <span className="text-muted-foreground dark:text-slate-500">•</span>
                <span className="flex items-center space-x-1">
                  <Award className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                  <span>Giao dịch an toàn</span>
                </span>
                <span className="text-muted-foreground dark:text-slate-500">•</span>
                <span className="flex items-center space-x-1">
                  <Clock className="h-3 w-3 text-yellow-600 dark:text-yellow-400" />
                  <span>Hỗ trợ 24/7</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}