-- Migration: Create transaction_tracking table
-- Description: Table to track processed CSGOEmpire transactions to avoid duplicates

CREATE TABLE IF NOT EXISTS `transaction_tracking` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `transaction_id` BIGINT NOT NULL UNIQUE,
    `steam_id` VARCHAR(20) DEFAULT NULL,
    `coin_amount` DECIMAL(10, 2) NOT NULL,
    `delta_amount` INT NOT NULL,
    `transaction_type` VARCHAR(50) NOT NULL,
    `transaction_key` VARCHAR(50) NOT NULL,
    `transaction_timestamp` BIGINT NOT NULL,
    `transaction_date` DATETIME NOT NULL,
    `matched_order_id` VARCHAR(50) DEFAULT NULL,
    `notification_sent` BOOLEAN DEFAULT FALSE,
    `notification_sent_at` TIMESTAMP NULL DEFAULT NULL,
    `processed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX `idx_transaction_id` (`transaction_id`),
    INDEX `idx_steam_id` (`steam_id`),
    INDEX `idx_transaction_timestamp` (`transaction_timestamp`),
    INDEX `idx_matched_order_id` (`matched_order_id`),
    INDEX `idx_notification_sent` (`notification_sent`),
    INDEX `idx_processed_at` (`processed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Comments for table structure:
-- id: Primary key, auto increment
-- transaction_id: CSGOEmpire transaction ID (unique)
-- steam_id: Steam ID from transaction data (if available)
-- coin_amount: Actual coin amount (delta / 100)
-- delta_amount: Raw delta amount from API
-- transaction_type: Type of transaction (Tip, Deposit, etc.)
-- transaction_key: Transaction key from API
-- transaction_timestamp: Original timestamp from API
-- transaction_date: Human readable date from API
-- matched_order_id: Order ID from buycoin table if matched
-- notification_sent: Whether Telegram notification was sent
-- notification_sent_at: When notification was sent
-- processed_at: When this record was created
-- created_at: Record creation time
-- updated_at: Last update time
