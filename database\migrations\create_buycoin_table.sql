-- Migration: Create buycoin table
-- Description: Table to store buy coin orders from users

CREATE TABLE IF NOT EXISTS `buycoin` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `order_id` VARCHAR(50) NOT NULL UNIQUE,
    `steam_id` VARCHAR(20) NOT NULL,
    `coin_amount` DECIMAL(10, 2) NOT NULL,
    `vnd_amount` INT NOT NULL,
    `bank_code` VARCHAR(10) NOT NULL,
    `account_number` VARCHAR(20) NOT NULL,
    `account_name` VARCHAR(255) DEFAULT NULL,
    `status` ENUM('created', 'completed', 'cancelled', 'failed','processing') DEFAULT 'created',
    `admin_note` TEXT DEFAULT NULL,
    `processed_by` VARCHAR(100) DEFAULT NULL,
    `processed_at` TIMESTAMP NULL DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_steam_id` (`steam_id`),
    INDEX `idx_order_id` (`order_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add some sample data for testing (optional)
-- INSERT INTO `buycoin` (
--     `order_id`, 
--     `steam_id`, 
--     `coin_amount`, 
--     `vnd_amount`, 
--     `bank_code`, 
--     `account_number`, 
--     `status`
-- ) VALUES 
-- ('****************', '*****************', 10.50, 8925, 'VCB', '**********', 'processing'),
-- ('****************', '*****************', 25.00, 21250, 'ICB', '**********', 'completed');

-- Comments for table structure:
-- id: Primary key, auto increment
-- order_id: Unique order identifier (BC + timestamp + random)
-- steam_id: Steam ID of the user selling coins
-- coin_amount: Amount of coins to sell (decimal with 2 places)
-- vnd_amount: VND amount user will receive (calculated)
-- bank_code: Bank code from VietQR API
-- account_number: User's bank account number
-- account_name: Account holder name (fetched from bank API)
-- status: Order status (processing, completed, cancelled, failed)
-- admin_note: Notes from admin during processing
-- processed_by: Admin who processed the order
-- processed_at: When the order was processed
-- created_at: When order was created
-- updated_at: Last update time
