"use client";

export default function FormMessages({ state }) {
  if (!state) return null;

  return (
    <>
      {/* Error Message */}
      {state?.errorServer && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
          <p className="text-red-700 dark:text-red-400 flex items-center space-x-2">
            <span>❌</span>
            <span>{state.message}</span>
          </p>
        </div>
      )}

      {/* Success Message */}
      {state?.success && (
        <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
           <p className="text-red-700 dark:text-red-600 flex items-center space-x-2">
            <span>Mã đơn hàng:</span>
            <span>{state.data.orderId}</span>
          </p>
          <p className="text-green-700 dark:text-green-400 flex items-center space-x-2">
            <span>✅</span>
            <span>Đơn hàng bán coin đã được tạo thành công! <PERSON>ui lòng gửi coin trong 5 phút tới id 76561199172430695</span>
          </p>
        </div>
      )}
    </>
  );
}


