import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ShieldX, Home, LogIn } from 'lucide-react';
import Link from 'next/link';

export default function AdminAccessDenied() {
    return (
        <div className="container mx-auto p-6 flex items-center justify-center min-h-[60vh]">
            <Card className="max-w-md w-full">
                <CardHeader className="text-center">
                    <div className="mx-auto mb-4 w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                        <ShieldX className="w-8 h-8 text-red-600 dark:text-red-400" />
                    </div>
                    <CardTitle className="text-2xl">Access Denied</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <Alert variant="destructive">
                        <ShieldX className="h-4 w-4" />
                        <AlertDescription>
                            Bạn không có quyền truy cập vào Admin Dashboard.
                            Chỉ có admin được phép truy cập khu vực này.
                        </AlertDescription>
                    </Alert>

                    <div className="text-center text-sm text-muted-foreground space-y-3">
                        <p>Nếu bạn là admin, vui lòng đăng nhập đúng tài khoản</p>
                    </div>

                    <div className="flex flex-col gap-2">
                        <Link href="/">
                            <Button className="w-full" variant="default">
                                <Home className="w-4 h-4 mr-2" />
                                Về trang chủ
                            </Button>
                        </Link>
                        <Link href="/login">
                            <Button className="w-full" variant="outline">
                                <LogIn className="w-4 h-4 mr-2" />
                                Đăng nhập lại
                            </Button>
                        </Link>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
