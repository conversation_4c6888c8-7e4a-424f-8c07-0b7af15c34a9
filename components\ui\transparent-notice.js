"use client";

"use client";

import { X, Info, AlertTriangle, <PERSON>Circle, Bell } from "lucide-react";
import { useState, useEffect } from "react";

export default function TransparentNotice({
  isVisible = true,
  message = "<PERSON><PERSON><PERSON> là thông báo mặc định",
  title = "Thông báo",
  type = "info", // "info", "warning", "success", "announcement"
  closeable = false,
  autoClose = false,
  autoCloseDelay = 5000,
  position = "top" // "top", "center", "bottom"
}) {
  const [visible, setVisible] = useState(isVisible);

  // Auto close functionality
  useEffect(() => {
    if (autoClose && visible) {
      const timer = setTimeout(() => {
        setVisible(false);
      }, autoCloseDelay);

      return () => clearTimeout(timer);
    }
  }, [autoClose, visible, autoCloseDelay]);

  if (!visible) return null;

  const handleClose = () => {
    setVisible(false);
  };

  const getTypeStyles = () => {
    switch (type) {
      case "warning":
        return {
          bg: "bg-yellow-500/10 border-yellow-500/30",
          text: "text-yellow-800 dark:text-yellow-200",
          icon: "text-yellow-600 dark:text-yellow-400",
          iconComponent: <AlertTriangle className="h-5 w-5" />
        };
      case "success":
        return {
          bg: "bg-green-500/10 border-green-500/30",
          text: "text-green-800 dark:text-green-200",
          icon: "text-green-600 dark:text-green-400",
          iconComponent: <CheckCircle className="h-5 w-5" />
        };
      case "announcement":
        return {
          bg: "bg-purple-500/10 border-purple-500/30",
          text: "text-purple-800 dark:text-purple-200",
          icon: "text-purple-600 dark:text-purple-400",
          iconComponent: <Bell className="h-5 w-5" />
        };
      default: // info
        return {
          bg: "bg-blue-500/10 border-blue-500/30",
          text: "text-blue-800 dark:text-blue-200",
          icon: "text-blue-600 dark:text-blue-400",
          iconComponent: <Info className="h-5 w-5" />
        };
    }
  };

  const getPositionStyles = () => {
    switch (position) {
      case "center":
        return "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50";
      case "bottom":
        return "fixed bottom-4 left-4 right-4 z-50 max-w-md mx-auto";
      default: // top
        return "relative mb-6";
    }
  };

  const styles = getTypeStyles();
  const positionClass = getPositionStyles();

  return (
    <div className={positionClass}>
      <div className={`
        backdrop-blur-md border rounded-xl p-4 shadow-lg
        ${styles.bg}
        transition-all duration-300 ease-in-out
        hover:shadow-xl hover:scale-[1.02]
      `}>
        <div className="flex items-start justify-between space-x-3">
          <div className="flex items-start space-x-3 flex-1">
            <div className={`flex-shrink-0 ${styles.icon}`}>
              {styles.iconComponent}
            </div>
            <div className="flex-1 min-w-0">
              <h3 className={`text-sm font-semibold ${styles.text} mb-1`}>
                {title}
              </h3>
              <p className={`text-sm ${styles.text} opacity-90 leading-relaxed`}>
                {message}
              </p>
            </div>
          </div>
          
          {closeable && (
            <button
              onClick={handleClose}
              className={`
                flex-shrink-0 p-1 rounded-lg transition-colors duration-200
                ${styles.icon} hover:bg-black/10 dark:hover:bg-white/10
              `}
              aria-label="Đóng thông báo"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
