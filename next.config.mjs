/** @type {import('next').NextConfig} */
const cspHeader = `
    default-src 'self' *.tawk.to;
    script-src 'self' *.jsdelivr.net *.tawk.to 'unsafe-eval' 'unsafe-inline';
    style-src 'self' *.tawk.to 'unsafe-inline';
    img-src 'self' img.vietqr.io api.vietqr.io community.fastly.steamstatic.com steamcommunity-a.akamaihd.net avatars.steamstatic.com *.tawk.to blob: data:;
    font-src 'self' *.tawk.to;
    connect-src 'self' *.tawk.to wss://*.tawk.to api.vietqr.io;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    upgrade-insecure-requests;
`
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'img.vietqr.io',
        port: '443',
        pathname: '/image/**',
      },
       {
        protocol: 'https',
        hostname: 'api.vietqr.io',
        port: '443',
        pathname: '/img/**',
      },
      {
        protocol: 'https',
        hostname: 'steamcommunity-a.akamaihd.net',
        port: '',
        pathname: '/economy/image/**',
      },
      {
        protocol: 'https',
        hostname: 'community.fastly.steamstatic.com',
        port: '',
        pathname: '/economy/image/**',
      }
    ],
  },
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: cspHeader.replace(/\n/g, ''),
          },
        ],
      },
    ]
  },
};

export default nextConfig;
