"use client";
import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Play, Square, RotateCcw, TestTube, CheckCircle, AlertCircle, Clock, Activity } from "lucide-react";

export default function CoinMonitorControl() {
    const [status, setStatus] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [lastUpdate, setLastUpdate] = useState(null);

    // Fetch worker status
    const fetchStatus = async () => {
        try {
            const response = await fetch('/api/admin/coin-monitor');
            const data = await response.json();

            if (data.success) {
                setStatus(data.status);
                setError(null);
                setLastUpdate(new Date());
            } else {
                setError(data.error);
            }
        } catch (err) {
            setError('Failed to fetch worker status');
            console.error('Error fetching status:', err);
        }
    };

    // Control worker
    const controlWorker = async (action) => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await fetch('/api/admin/coin-monitor', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ action })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Refresh status after action
                setTimeout(fetchStatus, 1000);
            } else {
                setError(data.error);
            }
        } catch (err) {
            setError(`Failed to ${action} worker`);
            console.error(`Error ${action} worker:`, err);
        } finally {
            setLoading(false);
        }
    };

    // Auto-refresh status every 10 seconds
    useEffect(() => {
        fetchStatus();
        const interval = setInterval(fetchStatus, 10000);
        return () => clearInterval(interval);
    }, []);

    const formatRuntime = (seconds) => {
        if (!seconds) return '0s';
        
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    };

    const formatLastCheck = (timestamp) => {
        if (!timestamp) return 'Never';
        
        const now = Date.now();
        const diff = Math.floor((now - timestamp) / 1000);
        
        if (diff < 60) return `${diff}s ago`;
        if (diff < 3600) return `${Math.floor(diff / 60)}m ago`;
        return new Date(timestamp).toLocaleTimeString();
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Coin Monitor Worker</h2>
                    <p className="text-gray-600 dark:text-gray-400">
                        Quản lý worker kiểm tra coin mới từ CSGOEmpire
                    </p>
                </div>
                
                {lastUpdate && (
                    <div className="text-sm text-gray-500">
                        Last updated: {lastUpdate.toLocaleTimeString()}
                    </div>
                )}
            </div>

            {/* Error Alert */}
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Status Card */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Activity className="h-5 w-5" />
                        Worker Status
                        {status && (
                            <Badge variant={status.isRunning ? "default" : "secondary"}>
                                {status.isRunning ? "Running" : "Stopped"}
                            </Badge>
                        )}
                    </CardTitle>
                    <CardDescription>
                        Current worker status and statistics
                    </CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-4">
                    {status ? (
                        <>
                            {/* Basic Info */}
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div className="space-y-1">
                                    <p className="text-sm font-medium">Status</p>
                                    <Badge variant={status.isRunning ? "default" : "secondary"}>
                                        {status.isRunning ? "Running" : "Stopped"}
                                    </Badge>
                                </div>
                                
                                <div className="space-y-1">
                                    <p className="text-sm font-medium">Runtime</p>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {formatRuntime(status.runtime)}
                                    </p>
                                </div>
                                
                                <div className="space-y-1">
                                    <p className="text-sm font-medium">Interval</p>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {status.intervalMs / 1000}s
                                    </p>
                                </div>
                                
                                <div className="space-y-1">
                                    <p className="text-sm font-medium">Last Check</p>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        {formatLastCheck(status.lastCheckTime)}
                                    </p>
                                </div>
                            </div>

                            <Separator />

                            {/* Statistics */}
                            <div>
                                <h4 className="font-medium mb-3">Statistics</h4>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium">Total Checks</p>
                                        <p className="text-2xl font-bold text-blue-600">
                                            {status.stats.totalChecks}
                                        </p>
                                    </div>
                                    
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium">Transactions Found</p>
                                        <p className="text-2xl font-bold text-green-600">
                                            {status.stats.transactionsFound}
                                        </p>
                                    </div>
                                    
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium">Notifications Sent</p>
                                        <p className="text-2xl font-bold text-purple-600">
                                            {status.stats.notificationsSent}
                                        </p>
                                    </div>
                                    
                                    <div className="space-y-1">
                                        <p className="text-sm font-medium">Errors</p>
                                        <p className="text-2xl font-bold text-red-600">
                                            {status.stats.errors}
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {/* Error Count Warning */}
                            {status.errorCount > 0 && (
                                <Alert variant="destructive">
                                    <AlertCircle className="h-4 w-4" />
                                    <AlertDescription>
                                        Worker has {status.errorCount} consecutive errors. 
                                        Max errors before auto-stop: {status.maxErrors}
                                    </AlertDescription>
                                </Alert>
                            )}
                        </>
                    ) : (
                        <div className="text-center py-8">
                            <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                            <p className="text-gray-500">Loading worker status...</p>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Control Buttons */}
            <Card>
                <CardHeader>
                    <CardTitle>Worker Controls</CardTitle>
                    <CardDescription>
                        Start, stop, or test the coin monitor worker
                    </CardDescription>
                </CardHeader>
                
                <CardContent>
                    <div className="flex flex-wrap gap-3">
                        <Button
                            onClick={() => controlWorker('start')}
                            disabled={loading || (status && status.isRunning)}
                            className="flex items-center gap-2"
                        >
                            <Play className="h-4 w-4" />
                            Start Worker
                        </Button>
                        
                        <Button
                            variant="destructive"
                            onClick={() => controlWorker('stop')}
                            disabled={loading || (status && !status.isRunning)}
                            className="flex items-center gap-2"
                        >
                            <Square className="h-4 w-4" />
                            Stop Worker
                        </Button>
                        
                        <Button
                            variant="outline"
                            onClick={() => controlWorker('restart')}
                            disabled={loading}
                            className="flex items-center gap-2"
                        >
                            <RotateCcw className="h-4 w-4" />
                            Restart Worker
                        </Button>
                        
                        <Button
                            variant="outline"
                            onClick={() => controlWorker('test-telegram')}
                            disabled={loading}
                            className="flex items-center gap-2"
                        >
                            <TestTube className="h-4 w-4" />
                            Test Telegram
                        </Button>
                        
                        <Button
                            variant="outline"
                            onClick={() => controlWorker('validate-config')}
                            disabled={loading}
                            className="flex items-center gap-2"
                        >
                            <CheckCircle className="h-4 w-4" />
                            Validate Config
                        </Button>
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}
