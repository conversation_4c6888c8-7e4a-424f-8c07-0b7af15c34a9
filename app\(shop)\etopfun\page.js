
import { fetchEtopfunItems, fetchHome, fetchUserInfo ,fetchLevelUser} from "@/lib/http"
import { checkAuth } from "@/lib/utils";
import { ErrorHome } from "@/components/common/Error";
export const metadata = {
  title: 'Auto bán shard etop',
  keywords: "shardetop shardetop.com, etopfun"
}
import { cookies } from "next/headers";
import Rate from "@/components/features/banner/Rate";
import Etopfun from "@/components/features/etopfun/EtopFun";
export default async function Page() {
  try {
    const cookieStore = cookies()
    let steamId = cookieStore.get('steamIdEtop')?.value || ''
    const data = await fetchHome()
    const { isLoggedIn } = await checkAuth(cookieStore)
    const { banks, coins } = data
    let settings = data.settings
    const notificationSetting = settings.find(setting => setting.name === "NOTIFICATION");
    const facebookSetting = settings.find(setting => setting.name === "FACEBOOK");
    const coinEtop = coins.find(setting => setting.name === "ETOP");
    let rate = coinEtop.rate
    if (isLoggedIn) {
      rate = coinEtop.rateUser
      const level = await fetchLevelUser()
       if (level && level?.id) {
       const rateLevel = level.rates.find(r => r.coinName=="ETOP")
        if (rateLevel) rate= rateLevel.value
      }
      const userInfo = await fetchUserInfo();
      if (userInfo?.lastSteamId)
        steamId = userInfo.lastSteamId
    }
    
    let allItems = await fetchEtopfunItems()
    if (!allItems) allItems = []
    return (
      <>
       
        <Rate rateBuy={coinEtop.buyRate}
          rateSell={rate}
          facebook={facebookSetting}
          type="etop"
          phone={"**********"}></Rate>
        <Etopfun
          items={allItems}
          banks={banks}
          steamId={steamId}
          rate={isLoggedIn ? coinEtop.rateUser : coinEtop.rate}>
        </Etopfun>
      </>
    )
  } catch (error) {
    return (
      <ErrorHome></ErrorHome>
    );
  }
}