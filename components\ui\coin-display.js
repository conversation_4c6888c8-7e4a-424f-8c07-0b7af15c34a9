"use client";
import { useState, useEffect } from "react";
import { CountUpAnimation } from "@/components/ui/floating-elements";

export default function CoinDisplay({ coinBalance }) {
  const [activeTab, setActiveTab] = useState("buy");

  // Listen for tab changes from OrderTabs
  useEffect(() => {
    const handleTabChange = (event) => {
      setActiveTab(event.detail.tab);
    };

    window.addEventListener('orderTabChange', handleTabChange);
    return () => window.removeEventListener('orderTabChange', handleTabChange);
  }, []);

  const getDisplayData = () => {
    switch (activeTab) {
      case "buy":
        return {
          title: "Coin Có Sẵn",
          value: (coinBalance / 100).toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2,
          }),
          bgClass: "from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20",
          borderClass: "border-blue-200 dark:border-blue-800 hover:border-blue-400 dark:hover:border-blue-600",
          textClass: "from-blue-600 to-purple-600"
        };
      case "sell":
        return {
          title: "Coin Có Thể Thu Mua",
          value: (18456.23).toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2,
          }),
          bgClass: "from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20",
          borderClass: "border-orange-200 dark:border-orange-800 hover:border-orange-400 dark:hover:border-orange-600",
          textClass: "from-orange-600 to-red-600"
        };
      case "support":
        return {
          title: "Coin Có Sẵn",
          value: (coinBalance / 100).toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2,
          }),
          bgClass: "from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20",
          borderClass: "border-green-200 dark:border-green-800 hover:border-green-400 dark:hover:border-green-600",
          textClass: "from-green-600 to-blue-600"
        };
      default:
        return {
          title: "Coin Có Sẵn",
          value: (coinBalance / 100).toLocaleString('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2,
          }),
          bgClass: "from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20",
          borderClass: "border-blue-200 dark:border-blue-800 hover:border-blue-400 dark:hover:border-blue-600",
          textClass: "from-blue-600 to-purple-600"
        };
    }
  };

  const displayData = getDisplayData();

  return (
    <>
      <h3  className=" hidden md:block text-2xl font-bold text-gray-800 dark:text-gray-100 mb-4">
        {displayData.title}
      </h3>
      <div className={` bg-gradient-to-r ${displayData.bgClass} rounded-xl p-4 md:p-6 border ${displayData.borderClass} transition-all duration-300`}>
        <div className="flex md:hidden items-center justify-center">
          <span className="text-base font-bold pr-4 text-gray-800 dark:text-gray-100">
            {displayData.title}:
          </span>
         <CountUpAnimation
          value={displayData.value}
          className={`text-4xl font-bold bg-gradient-to-r ${displayData.textClass} bg-clip-text text-transparent mb-2 block`}
        />
        </div>
         <CountUpAnimation
          value={displayData.value}
          className={`hidden md:block text-4xl font-bold bg-gradient-to-r ${displayData.textClass} bg-clip-text text-transparent mb-2 block`}
        />
        <p className="text-gray-600 dark:text-gray-400 text-lg hidden md:block">
          Coin CSGOEmpire
        </p>
      </div>
      
    </>
  );
}
