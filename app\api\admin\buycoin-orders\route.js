import { execute } from '@/lib/db';
import { NextResponse } from 'next/server';

// GET - Fetch all buycoin orders with stats
export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const page = Math.max(1, parseInt(searchParams.get('page')) || 1);
        const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit')) || 50));
        const status = searchParams.get('status');
        const search = searchParams.get('search');

        const offset = (page - 1) * limit;

        // Validate numeric parameters
        if (isNaN(page) || isNaN(limit) || isNaN(offset)) {
            return NextResponse.json({
                success: false,
                error: 'Invalid pagination parameters'
            }, { status: 400 });
        }
        
        // Build WHERE clause
        let whereClause = '1=1';
        let queryParams = [];
        
        if (status && status !== 'all') {
            whereClause += ' AND status = ?';
            queryParams.push(status);
        }
        
        if (search) {
            whereClause += ' AND (order_id LIKE ? OR steam_id LIKE ? OR account_number LIKE ?)';
            const searchPattern = `%${search}%`;
            queryParams.push(searchPattern, searchPattern, searchPattern);
        }
        
        // Get orders with pagination
        const [orders] = await execute(`
            SELECT
                id,
                order_id,
                steam_id,
                coin_amount,
                vnd_amount,
                bank_code,
                account_number,
                account_name,
                status,
                admin_note,
                processed_by,
                processed_at,
                created_at,
                updated_at
            FROM buycoin
            WHERE ${whereClause}
            ORDER BY created_at DESC
            LIMIT ${limit} OFFSET ${offset}
        `, queryParams);
        
        // Get total count
        const [countResult] = await execute(`
            SELECT COUNT(*) as total
            FROM buycoin 
            WHERE ${whereClause}
        `, queryParams);
        
        const total = countResult[0].total;
        
        // Get stats
        const [statsResult] = await execute(`
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'created' THEN 1 ELSE 0 END) as created,
                SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
            FROM buycoin
        `);
        
        const stats = statsResult[0];
        
        return NextResponse.json({
            success: true,
            orders,
            stats,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        });
        
    } catch (error) {
        console.error('Error fetching buycoin orders:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to fetch orders'
        }, { status: 500 });
    }
}

// POST - Create new buycoin order (if needed for admin)
export async function POST(request) {
    try {
        const body = await request.json();
        const {
            order_id,
            steam_id,
            coin_amount,
            vnd_amount,
            bank_code,
            account_number,
            account_name,
            status = 'created'
        } = body;
        
        // Validate required fields
        if (!order_id || !steam_id || !coin_amount || !vnd_amount || !bank_code || !account_number) {
            return NextResponse.json({
                success: false,
                error: 'Missing required fields'
            }, { status: 400 });
        }
        
        // Check if order_id already exists
        const [existingOrder] = await execute(
            'SELECT id FROM buycoin WHERE order_id = ?',
            [order_id]
        );
        
        if (existingOrder.length > 0) {
            return NextResponse.json({
                success: false,
                error: 'Order ID already exists'
            }, { status: 400 });
        }
        
        // Insert new order
        const [result] = await db.execute(`
            INSERT INTO buycoin (
                order_id, steam_id, coin_amount, vnd_amount, 
                bank_code, account_number, account_name, status,
                created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `, [
            order_id, steam_id, coin_amount, vnd_amount,
            bank_code, account_number, account_name, status
        ]);
        
        return NextResponse.json({
            success: true,
            message: 'Order created successfully',
            order_id: result.insertId
        });
        
    } catch (error) {
        console.error('Error creating buycoin order:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to create order'
        }, { status: 500 });
    }
}
