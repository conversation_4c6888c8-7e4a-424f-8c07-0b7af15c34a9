"use client";
import { useState, useEffect } from "react";

const STORAGE_KEYS = {
  BANK_INFO: 'buycoin_bank_info'
};

export function useFormStorage() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [bankInfo, setBankInfo] = useState(null);

  // Helper function to safely get from localStorage
  const getStoredValue = (key) => {
    if (typeof window === 'undefined') return null;
    try {
      const value = localStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.warn('Error reading from localStorage:', error);
      return null;
    }
  };

  // Helper function to safely set to localStorage
  const setStoredValue = (key, value) => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn('Error writing to localStorage:', error);
    }
  };

  // Load stored data on mount
  useEffect(() => {
    const storedBankInfo = getStoredValue(STORAGE_KEYS.BANK_INFO);
    setBankInfo(storedBankInfo);
    setIsLoaded(true);
  }, []);

  // Save bank info
  const saveBankInfo = (bankCode, accountNumber) => {
    const newBankInfo = { bankCode, accountNumber };
    setBankInfo(newBankInfo);
    setStoredValue(STORAGE_KEYS.BANK_INFO, newBankInfo);
  };

  // Clear bank data
  const clearBankData = () => {
    setBankInfo(null);
    setStoredValue(STORAGE_KEYS.BANK_INFO, null);
  };

  return {
    isLoaded,
    bankInfo,
    saveBankInfo,
    clearBankData
  };
}
