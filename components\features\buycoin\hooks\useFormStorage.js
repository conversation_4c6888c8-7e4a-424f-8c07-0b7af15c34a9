"use client";
import { useState, useEffect } from "react";

const STORAGE_KEYS = {
  STEAM_IDS: 'buycoin_steam_ids',
  BANK_INFO: 'buycoin_bank_info'
};

export function useFormStorage() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [steamIds, setSteamIds] = useState([]);
  const [bankInfo, setBankInfo] = useState(null);

  // Helper function to safely get from localStorage
  const getStoredValue = (key) => {
    if (typeof window === 'undefined') return null;
    try {
      const value = localStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.warn('Error reading from localStorage:', error);
      return null;
    }
  };

  // Helper function to safely set to localStorage
  const setStoredValue = (key, value) => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn('Error writing to localStorage:', error);
    }
  };

  // Load stored data on mount
  useEffect(() => {
    const storedSteamIds = getStoredValue(STORAGE_KEYS.STEAM_IDS) || [];
    const storedBankInfo = getStoredValue(STORAGE_KEYS.BANK_INFO);

    setSteamIds(storedSteamIds);
    setBankInfo(storedBankInfo);
    setIsLoaded(true);
  }, []);

  // Add new Steam ID
  const addSteamId = (steamId) => {
    if (!steamId || steamIds.includes(steamId)) return;

    const updatedSteamIds = [...steamIds, steamId];
    setSteamIds(updatedSteamIds);
    setStoredValue(STORAGE_KEYS.STEAM_IDS, updatedSteamIds);
  };

  // Remove Steam ID
  const removeSteamId = (steamId) => {
    const updatedSteamIds = steamIds.filter(id => id !== steamId);
    setSteamIds(updatedSteamIds);
    setStoredValue(STORAGE_KEYS.STEAM_IDS, updatedSteamIds);
  };

  // Save bank info
  const saveBankInfo = (bankCode, accountNumber) => {
    const newBankInfo = { bankCode, accountNumber };
    setBankInfo(newBankInfo);
    setStoredValue(STORAGE_KEYS.BANK_INFO, newBankInfo);
  };

  // Clear all data
  const clearAllData = () => {
    setSteamIds([]);
    setBankInfo(null);
    setStoredValue(STORAGE_KEYS.STEAM_IDS, []);
    setStoredValue(STORAGE_KEYS.BANK_INFO, null);
  };

  return {
    isLoaded,
    steamIds,
    bankInfo,
    addSteamId,
    removeSteamId,
    saveBankInfo,
    clearAllData
  };
}
