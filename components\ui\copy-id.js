'use client'

import { useState } from 'react'

export default function CopyId({ id = '76561199172430695' }) {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(id)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  return (
    <div className="flex items-center justify-between bg-gray-100 dark:bg-slate-600 p-2 rounded">
      <span className="font-semibold text-violet-800 dark:text-indigo-300 font-mono">{id}</span>
      <button 
        onClick={handleCopy}
        className="ml-2 px-2 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors"
        title="Copy ID"
      >
        {copied ? 'Copied!' : 'Copy'}
      </button>
    </div>
  )
}
