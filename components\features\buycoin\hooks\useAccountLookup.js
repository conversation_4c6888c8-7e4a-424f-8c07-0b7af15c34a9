"use client";
import { useState, useEffect, useCallback } from "react";

export function useAccountLookup() {
  const [selectedBank, setSelectedBank] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [accountName, setAccountName] = useState("");
  const [loadingAccountName, setLoadingAccountName] = useState(false);

  // Helper: <PERSON><PERSON><PERSON> danh sách ngân hàng
  const fetchBanks = async () => {
    const response = await fetch("/api/banks");
    if (!response.ok) throw new Error("Không thể lấy danh sách ngân hàng");
    const data = await response.json();
    if (!data.success || !data.data) throw new Error("Danh sách ngân hàng không hợp lệ");
    return data.data;
  };

  // Helper: Tra cứu tên tài khoản
  const lookupAccountName = async (bankBin, accountNumber) => {
    const response = await fetch("/api/bank-account-lookup", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ bankBin, accountNumber }),
    });

    if (!response.ok) throw new Error("Không thể tra cứu số tài khoản");
    const result = await response.json();
    if (!result.success) throw new Error(result.error || "Có lỗi khi tra cứu số tài khoản");

    return result.accountName || "";
  };

  // Hàm gọi API tra cứu
  const fetchAccountName = useCallback(async () => {
    if (!selectedBank || !accountNumber || accountNumber.length < 6) {
      setAccountName("");
      return;
    }

    setLoadingAccountName(true);
    try {
      const banks = await fetchBanks();
      const bankInfo = banks.find((bank) => bank.code === selectedBank);

      if (!bankInfo || !bankInfo.bin) {
        throw new Error("Không tìm thấy thông tin ngân hàng");
      }

      const name = await lookupAccountName(bankInfo.bin, accountNumber);
      setAccountName(name);
    } catch (err) {
      setAccountName(err.message || "Có lỗi khi tra cứu số tài khoản");
    } finally {
      setLoadingAccountName(false);
    }
  }, [selectedBank, accountNumber]);

  // Debounce 1s
  useEffect(() => {
    const timeoutId = setTimeout(fetchAccountName, 1000);
    return () => clearTimeout(timeoutId);
  }, [fetchAccountName]);

  return {
    selectedBank,
    setSelectedBank,
    accountNumber,
    setAccountNumber,
    accountName,
    setAccountName,
    loadingAccountName,
  };
}