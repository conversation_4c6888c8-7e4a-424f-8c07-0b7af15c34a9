"use client";
import { useState, useEffect, useCallback } from "react";

export function useAccountLookup() {
  const [selectedBank, setSelectedBank] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [accountName, setAccountName] = useState("");
  const [loadingAccountName, setLoadingAccountName] = useState(false);

  // Fetch account name when bank and account number are selected
  const fetchAccountName = useCallback(async () => {
    if (selectedBank && accountNumber && accountNumber.length >= 6) {
      setLoadingAccountName(true);
      try {
        // First, get the bank list to find the bin for the selected bank code
        const banksResponse = await fetch('/api/banks');
        const banksResult = await banksResponse.json();

        if (!banksResult.success || !banksResult.data) {
          setAccountName("");
          return;
        }

        // Find the bank with the selected code to get its bin
        const selectedBankInfo = banksResult.data.find(bank => bank.code === selectedBank);
        if (!selectedBankInfo || !selectedBankInfo.bin) {
          setAccountName("");
          return;
        }

        // Use fetch instead of server action to avoid async/await issues in client component
        const response = await fetch('/api/bank-account-lookup', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            bankBin: selectedBankInfo.bin,
            accountNumber: accountNumber
          })
        });

        if (response.ok) {
          const result = await response.json();
          setAccountName(result.accountName || "");
        } else {
          setAccountName("");
        }
      } catch (error) {
        console.error('Error fetching account name:', error);
        setAccountName("");
      } finally {
        setLoadingAccountName(false);
      }
    } else {
      setAccountName("");
    }
  }, [selectedBank, accountNumber]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchAccountName();
    }, 1000); // Debounce 500ms

    return () => clearTimeout(timeoutId);
  }, [fetchAccountName]);

  return {
    selectedBank,
    setSelectedBank,
    accountNumber,
    setAccountNumber,
    accountName,
    setAccountName,
    loadingAccountName
  };
}
