"use client";
import { clsx } from "clsx";
import { motion } from "framer-motion";
import { Co<PERSON>, TrendingUp, Shield, Z<PERSON>, Star, Heart } from "lucide-react";
import { useMemo } from "react";

const ICONS = [Coins, TrendingUp, Shield, Zap, Star, Heart];

export default function FloatingElements() {
  const floatingElements = useMemo(() => {
    const elements = [];
    const count = 12; // giảm số lượng nếu cần tối ưu thêm
    for (let i = 0; i < count; i++) {
      elements.push({
        icon: ICONS[i % ICONS.length],
        x: `${Math.random() * 100}%`,
        y: `${Math.random() * 100}%`,
        duration: 6 + Math.random() * 4,  // 6–10s
        delay: Math.random() * 4,         // delay random
        scale: 0.6 + Math.random() * 0.5, // random scale
      });
    }
    return elements;
  }, []);

  return (
    <div className="fixed inset-0 pointer-events-none z-10 overflow-hidden">
      {floatingElements.map((element, index) => {
        const IconComponent = element.icon;
        return (
          <div
            key={index}
            className={clsx("absolute floating-element")}
            style={{
              left: element.x,
              top: element.y,
              animationDuration: `${element.duration}s`,
              animationDelay: `${element.delay}s`,
              transform: `scale(${element.scale})`,
            }}
          >
            <div className="relative bg-white/5 dark:bg-slate-700/10 rounded-full p-2 border border-white/10 shadow-md">
              <IconComponent className="h-5 w-5 text-blue-400 dark:text-blue-300" />
            </div>
          </div>
        );
      })}
    </div>
  );
}

export function FloatingCard({ children, delay = 0, className = "" }) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.6,
        delay,
        ease: [0.25, 0.25, 0, 1],
      }}
      whileHover={{
        y: -5,
        scale: 1.02,
        transition: { duration: 0.2 },
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

export function PulseButton({ children, className = "", ...props }) {
  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      animate={{
        boxShadow: [
          "0 0 0 0 rgba(59, 130, 246, 0.4)",
          "0 0 0 10px rgba(59, 130, 246, 0)",
        ],
      }}
      transition={{
        boxShadow: {
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut",
        },
      }}
      className={`relative overflow-hidden ${className}`}
      {...props}
    >
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 opacity-0"
        whileHover={{ opacity: 0.1 }}
        transition={{ duration: 0.3 }}
      />
      {children}
    </motion.button>
  );
}

export function GlowingText({ children, className = "" }) {
  return (
    <motion.div
      className={`relative ${className}`}
      animate={{
        textShadow: [
          "0 0 10px rgba(59, 130, 246, 0.5)",
          "0 0 20px rgba(139, 92, 246, 0.5)",
          "0 0 10px rgba(59, 130, 246, 0.5)",
        ],
      }}
      transition={{
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut",
      }}
    >
      {children}
    </motion.div>
  );
}

export function CountUpAnimation({ value, duration = 2, className = "" }) {
  return (
    <motion.span
      className={className}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.span
        initial={{ scale: 0.5 }}
        animate={{ scale: 1 }}
        transition={{ duration, ease: "easeOut" }}
      >
        {value}
      </motion.span>
    </motion.span>
  );
}
