'use server';
import { fetchHome } from '@/lib/http.js';
// import 'server-only'; // Commented out for testing

import { execute } from '../lib/db.js';

/**
 * Transaction Tracking Service (Unified)
 * Handles tracking of both processed and pending transactions in one table
 */

/**
 * Save a transaction to pending tracking
 * @param {Object} transactionData - Transaction data from CSGOEmpire
 * @returns {Promise<Object>} Save result
 */
export async function savePendingTransaction(transactionData) {
    try {
        const query = `
            INSERT INTO transaction_tracking (
                transaction_id,
                steam_id,
                coin_amount,
                delta_amount,
                transaction_type,
                transaction_key,
                transaction_timestamp,
                transaction_date,
                estimated_vnd,
                status,
                first_detected_at,
                last_checked_at,
                check_count,
                notification_sent,
                processed_at
            ) VALUES (?, ?, ?, ?, ?, ?,?, ?, ?, 'pending', NOW(), NOW(), 1, FALSE, NOW())
            ON DUPLICATE KEY UPDATE
                last_checked_at = NOW(),
                check_count = check_count + 1,
                updated_at = NOW()
        `;

        const coinAmount = transactionData.delta / 100;
        const transactionDate = new Date(transactionData.timestamp * 1000);
        const home = await fetchHome()
        let estimatedVnd = 0;
        if (home) {
            const { coinBuyRate } = home
            estimatedVnd = Math.round(coinAmount * coinBuyRate * 1000);
        }
        const [result] = await execute(query, [
            transactionData.id,
            transactionData.data?.steam_id || null,
            coinAmount,
            transactionData.delta,
            transactionData.type,
            transactionData.key,
            transactionData.timestamp,
            transactionDate.toISOString().slice(0, 19).replace('T', ' '),
            estimatedVnd
        ]);

        return {
            success: true,
            insertId: result.insertId,
            affectedRows: result.affectedRows
        };

    } catch (error) {
        console.error('Error saving pending transaction:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Get all pending transactions that haven't expired yet
 * @returns {Promise<Object>} Pending transactions result
 */
export async function getPendingTransactions() {
    try {
        const query = `
            SELECT * FROM transaction_tracking
            WHERE status = 'pending'
            AND first_detected_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ORDER BY first_detected_at ASC
        `;

        const [rows] = await execute(query);

        return {
            success: true,
            data: rows
        };

    } catch (error) {
        console.error('Error getting pending transactions:', error);
        return {
            success: false,
            error: error.message,
            data: []
        };
    }
}

/**
 * Get expired pending transactions (older than 5 minutes)
 * @returns {Promise<Object>} Expired transactions result
 */
export async function getExpiredPendingTransactions() {
    try {
        const query = `
            SELECT * FROM transaction_tracking
            WHERE status = 'pending'
            AND first_detected_at <= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ORDER BY first_detected_at ASC
        `;

        const [rows] = await execute(query);

        return {
            success: true,
            data: rows
        };

    } catch (error) {
        console.error('Error getting expired pending transactions:', error);
        return {
            success: false,
            error: error.message,
            data: []
        };
    }
}

/**
 * Mark a pending transaction as matched with an order
 * @param {number} transactionId - Transaction ID
 * @param {string} orderId - Matched order ID
 * @returns {Promise<Object>} Update result
 */
export async function markTransactionAsMatched(transactionId, orderId) {
    try {
        const updateSql = `
      UPDATE transaction_tracking
      SET status = 'matched',
          order_found = TRUE,
          order_found_at = NOW(),
          matched_order_id = ?,
          updated_at = NOW()
      WHERE transaction_id = ? AND status = 'pending'
    `;

        const [result] = await execute(updateSql, [orderId, transactionId]);

        // Không có dòng nào bị ảnh hưởng => không tìm thấy hoặc không còn ở trạng thái pending
        if (!result || result.affectedRows === 0) {
            return {
                success: false,
                reason: 'not_found_or_not_pending',
                affectedRows: 0,
            };
        }

        const [rows] = await execute(
            `SELECT * FROM transaction_tracking WHERE transaction_id = ? LIMIT 1`,
            [transactionId]
        );

        return {
            success: true,
            affectedRows: result.affectedRows,
            changedRows: result.changedRows, // nếu driver có
            data: rows?.[0] ?? null,
        };
    } catch (error) {
        console.error('Error marking transaction as matched:', error);
        return {
            success: false,
            error: error.message,
        };
    }
}

/**
 * Mark a pending transaction as expired
 * @param {number} transactionId - Transaction ID
 * @returns {Promise<Object>} Update result
 */
export async function markTransactionAsExpired(transactionId) {
    try {
        const query = `
            UPDATE transaction_tracking
            SET status = 'expired',
                expired = TRUE,
                expired_at = NOW(),
                updated_at = NOW()
            WHERE transaction_id = ? AND status = 'pending'
        `;

        const [result] = await execute(query, [transactionId]);

        return {
            success: true,
            affectedRows: result.affectedRows
        };

    } catch (error) {
        console.error('Error marking transaction as expired:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Update notification status for a pending transaction
 * @param {number} transactionId - Transaction ID
 * @param {boolean} notificationSent - Whether notification was sent successfully
 * @returns {Promise<Object>} Update result
 */
export async function updatePendingTransactionNotification(transactionId, notificationSent = true) {
    try {
        const query = `
            UPDATE transaction_tracking
            SET notification_sent = ?,
                notification_sent_at = ${notificationSent ? 'NOW()' : 'NULL'},
                updated_at = NOW()
            WHERE transaction_id = ?
        `;

        const [result] = await execute(query, [notificationSent, transactionId]);

        return {
            success: true,
            affectedRows: result.affectedRows
        };

    } catch (error) {
        console.error('Error updating pending transaction notification:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Check if a transaction is already being tracked as pending
 * @param {number} transactionId - Transaction ID
 * @returns {Promise<Object>} Check result
 */
export async function isNewTransaction(transactionId) {
    try {
        const query = `
            SELECT id, status FROM transaction_tracking
            WHERE transaction_id = ?
        `;

        const [rows] = await execute(query, [transactionId]);

        return {
            success: true,
            isNew: rows.length > 0,
            data: rows.length > 0 ? rows[0] : null
        };

    } catch (error) {
        console.error('Error checking if transaction is pending:', error);
        return {
            success: false,
            error: error.message,
            isPending: false,
            data: null
        };
    }
}

/**
 * Clean up old transactions (older than 24 hours)
 * @returns {Promise<Object>} Cleanup result
 */
export async function cleanupOldPendingTransactions() {
    try {
        const query = `
            DELETE FROM transaction_tracking
            WHERE status IN ('expired', 'cancelled')
            AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
        `;

        const [result] = await execute(query);

        return {
            success: true,
            deletedRows: result.affectedRows
        };

    } catch (error) {
        console.error('Error cleaning up old transactions:', error);
        return {
            success: false,
            error: error.message,
            deletedRows: 0
        };
    }
}
