"use strict";
import { useState } from "react";
import { AlertCircle, RefreshCw } from "lucide-react";

export default function QrCode({ data }) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  if (!data || !data.bank || !data.amount) {
    return (
      <div className="flex flex-col items-center justify-center h-64 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <AlertCircle className="h-12 w-12 text-gray-400 mb-2" />
        <p className="text-gray-500 text-center">Không thể tạo mã QR</p>
        <p className="text-gray-400 text-sm">Thiếu thông tin thanh toán</p>
      </div>
    );
  }

  const money = data.amount || 0;
  const bankName = data.bank.bankName || '';
  const bankNo = data.bank.bankNo || '';
  const accountName = data.bank.accountName || '';
  const content = data.content || '';

  const qrUrl = `https://img.vietqr.io/image/${bankName}-${bankNo}-compact2.jpg?amount=${money}&addInfo=${encodeURIComponent(content)}&accountName=${encodeURIComponent(accountName)}`;

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
    setImageError(false);
  };

  const retryLoadImage = () => {
    setImageError(false);
    setIsLoading(true);
  };

  if (imageError) {
    return (
      <div className="flex flex-col items-center justify-center h-64 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <AlertCircle className="h-12 w-12 text-red-400 mb-2" />
        <p className="text-red-500 text-center mb-2">Không thể tải mã QR</p>
        <button
          onClick={retryLoadImage}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          <RefreshCw className="h-4 w-4" />
          <span>Thử lại</span>
        </button>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex items-center justify-center">
      <div className="relative w-full h-full">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          </div>
        )}
        <img
          src={qrUrl}
          alt={`Mã QR cho thanh toán qua ngân hàng ${bankName}`}
          onError={handleImageError}
          onLoad={handleImageLoad}
          className={`w-full h-full object-contain rounded-md transition-opacity duration-300 ${isLoading ? 'opacity-0' : 'opacity-100'}`}
        />
      </div>
    </div>
  );
}