'use server';
import 'server-only';
import { <PERSON><PERSON> } from "zca-js";
import fs from "fs";

const ZALO_TARGET_USER_ID = process.env.ZALO_TARGET_USER_ID;

let zaloClient = null; // Singleton instance
let isLoggedIn = false;

function createZaloInstance() {
  return new Zalo({
    selfListen: false,
    checkUpdate: true,
    logging: true,
  });
}

async function initializeZalo() {
  if (zaloClient && isLoggedIn) {
    return { success: true, message: 'Already logged in' };
  }

  // Nếu đã có instance nhưng chưa login
  if (!zaloClient) {
    if (!globalThis.__ZALO_CLIENT__) {
      globalThis.__ZALO_CLIENT__ = createZaloInstance();
    }
    zaloClient = globalThis.__ZALO_CLIENT__;
  }

  try {
    const cookie = JSON.parse(fs.readFileSync("./cookie.json", "utf-8"));

    const api = await zalo<PERSON>lient.login({
      cookie,
      imei: 'd4e0260d-e7b4-45ad-838e-e6cff78e9105-d2ad6785d256851dd366703bdc61aa61',
      userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"
    });

    api.listener.start();
    isLoggedIn = true;
    globalThis.__ZALO_API__ = api; // Lưu API login đã thành công

    return { success: true, message: 'Zalo login successful' };
  } catch (error) {
    console.error('Error initializing Zalo:', error);
    isLoggedIn = false;
    return { success: false, message: error.message };
  }
}

export async function sendZaloMessage(message, options = {}) {
  try {
    if (!isLoggedIn || !globalThis.__ZALO_API__) {
      const initResult = await initializeZalo();
      if (!initResult.success) {
        throw new Error(`Failed to initialize Zalo: ${initResult.message}`);
      }
    }

    if (!ZALO_TARGET_USER_ID) {
      throw new Error('ZALO_TARGET_USER_ID environment variable is not set');
    }

    await globalThis.__ZALO_API__.sendMessage({ msg: message }, ZALO_TARGET_USER_ID);

    return { success: true, message: 'Message sent successfully' };
  } catch (error) {
    console.error('Error sending Zalo message:', error);
    if (error.message.includes('login') || error.message.includes('auth')) {
      isLoggedIn = false;
    }
    return { success: false, error: error.message };
  }
}

export async function validateZaloConfig() {
  const errors = [];
  if (!ZALO_TARGET_USER_ID) {
    errors.push('ZALO_TARGET_USER_ID environment variable is not set');
  }
  return { isValid: errors.length === 0, errors };
}

export async function getZaloStatus() {
  return {
    isLoggedIn,
    hasApi: !!globalThis.__ZALO_API__,
    config: { hasTargetUserId: !!ZALO_TARGET_USER_ID },
  };
}