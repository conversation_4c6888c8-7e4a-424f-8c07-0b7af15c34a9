'use server';
import 'server-only';

/**
 * Telegram Bot Service
 * Handles sending notifications via Telegram Bot API
 */

const TELEGRAM_BOT_TOKEN = process.env.TELEGRAM_BOT_TOKEN || "**********************************************";
const TELEGRAM_CHAT_ID = process.env.TELEGRAM_CHAT_ID || "1725032665";
const TELEGRAM_API_BASE = 'https://api.telegram.org/bot';

/**
 * Send a message to Telegram chat
 * @param {string} message - Message to send
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} Send result
 */
export async function sendTelegramMessage(message, options = {}) {
    try {
        if (!TELEGRAM_BOT_TOKEN) {
            throw new Error('TELEGRAM_BOT_TOKEN environment variable is not set');
        }

        if (!TELEGRAM_CHAT_ID) {
            throw new Error('TELEGRAM_CHAT_ID environment variable is not set');
        }

        const url = `${TELEGRAM_API_BASE}${TELEGRAM_BOT_TOKEN}/sendMessage`;
        
        const payload = {
            chat_id: TELEGRAM_CHAT_ID,
            text: message,
            parse_mode: options.parseMode || 'HTML',
            disable_web_page_preview: options.disableWebPagePreview || true,
            disable_notification: options.disableNotification || false
        };

        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload)
        });

        const data = await response.json();

        if (!response.ok || !data.ok) {
            throw new Error(`Telegram API error: ${data.description || response.statusText}`);
        }

        return {
            success: true,
            messageId: data.result.message_id,
            data: data.result
        };

    } catch (error) {
        console.error('Error sending Telegram message:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Send notification about new coin deposit
 * @param {Object} transactionData - Transaction data
 * @param {Object} orderData - Matching buycoin order data
 * @returns {Promise<Object>} Send result
 */
export async function sendCoinDepositNotification(transactionData, orderData) {
    try {
        const coinAmount = transactionData.delta_coins;
        const steamId = transactionData.data?.steam_id;
        const displayName = transactionData.data?.display_name || 'Unknown';
        const transactionId = transactionData.id;
        const transactionDate = transactionData.date;
        
        const orderInfo = orderData ? {
            orderId: orderData.order_id,
            expectedCoin: orderData.coin_amount,
            vndAmount: orderData.vnd_amount,
            bankCode: orderData.bank_code,
            accountNumber: orderData.account_number,
            accountName: orderData.account_name,
            createdAt: orderData.created_at
        } : null;

        let message = `🎯 <b>PHÁT HIỆN COIN MỚI!</b>\n\n`;
        message += `💰 <b>Số coin:</b> ${coinAmount}\n`;
        message += `💰 <b>Số tiền:</b> ${transactionData.estimated_vnd}\n`;
        message += `👤 <b>Người gửi:</b> ${displayName}\n`;
        message += `🆔 <b>Steam ID:</b> <code>${steamId}</code>\n`;
        message += `📅 <b>Thời gian:</b> ${transactionDate}\n`;
        message += `🔗 <b>Transaction ID:</b> <code>${transactionId}</code>\n\n`;

        if (orderInfo) {
            message += `✅ <b>KHỚP ĐƠN HÀNG!</b>\n`;
            message += `📋 <b>Mã đơn:</b> <code>${orderInfo.orderId}</code>\n`;
            message += `💎 <b>Coin mong đợi:</b> ${orderInfo.expectedCoin}\n`;
            message += `💵 <b>Số tiền VND:</b> ${orderInfo.vndAmount.toLocaleString('vi-VN')} VND\n`;
            message += `🏦 <b>Ngân hàng:</b> ${orderInfo.bankCode}\n`;
            message += `💳 <b>STK:</b> <code>${orderInfo.accountNumber}</code>\n`;
            message += `👤 <b>Chủ TK:</b> ${orderInfo.accountName || 'Chưa xác định'}\n`;
            message += `⏰ <b>Tạo đơn lúc:</b> ${new Date(orderInfo.createdAt).toLocaleString('vi-VN')}\n\n`;
            
            // Check if coin amounts match
            const expectedCoin = parseFloat(orderInfo.expectedCoin);
            const receivedCoin = parseFloat(coinAmount);
            
            if (Math.abs(expectedCoin - receivedCoin) < 0.01) {
                message += `✅ <b>Số coin khớp chính xác!</b>`;
            } else {
                message += `⚠️ <b>Số coin không khớp!</b>\n`;
                message += `Expected: ${expectedCoin}, Received: ${receivedCoin}`;
            }
        } else {
            message += `❌ <b>KHÔNG TÌM THẤY ĐƠN HÀNG KHỚP</b>\n`;
            message += `Vui lòng kiểm tra thủ công.`;
        }

        return await sendTelegramMessage(message, {
            parseMode: 'HTML',
            disableNotification: false
        });

    } catch (error) {
        console.error('Error sending coin deposit notification:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Send worker status notification
 * @param {string} status - Worker status (started, stopped, error)
 * @param {string} message - Additional message
 * @returns {Promise<Object>} Send result
 */
export async function sendWorkerStatusNotification(status, message = '') {
    try {
        let emoji = '🔄';
        let statusText = status.toUpperCase();
        
        switch (status.toLowerCase()) {
            case 'started':
                emoji = '✅';
                break;
            case 'stopped':
                emoji = '🛑';
                break;
            case 'error':
                emoji = '❌';
                break;
            case 'warning':
                emoji = '⚠️';
                break;
        }

        const notificationMessage = `${emoji} <b>WORKER ${statusText}</b>\n\n${message}`;
        
        return await sendTelegramMessage(notificationMessage, {
            parseMode: 'HTML',
            disableNotification: status === 'started' || status === 'stopped'
        });

    } catch (error) {
        console.error('Error sending worker status notification:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Validate Telegram configuration
 * @returns {Object} Validation result
 */
export async function validateTelegramConfig() {
    const errors = [];

    if (!TELEGRAM_BOT_TOKEN) {
        errors.push('TELEGRAM_BOT_TOKEN environment variable is not set');
    }

    if (!TELEGRAM_CHAT_ID) {
        errors.push('TELEGRAM_CHAT_ID environment variable is not set');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Test Telegram connection
 * @returns {Promise<Object>} Test result
 */
export async function testTelegramConnection() {
    try {
        const testMessage = `🧪 <b>Test Connection</b>\n\nTelegram bot is working correctly!\n⏰ ${new Date().toLocaleString('vi-VN')}`;

        const result = await sendTelegramMessage(testMessage, {
            parseMode: 'HTML'
        });

        return result;

    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Send notification about transaction found but no matching order yet
 * @param {Object} transactionData - Transaction data
 * @returns {Promise<Object>} Send result
 */
export async function sendTransactionPendingNotification(transactionData) {
    try {
        const coinAmount = transactionData.delta_coins || (transactionData.delta / 100);
        const steamId = transactionData.data?.steam_id;
        const displayName = transactionData.data?.display_name || 'Unknown';
        const transactionId = transactionData.id;
        const transactionDate = transactionData.date;

        let message = `⏳ <b>TRANSACTION ĐANG CHỜ ORDER!</b>\n\n`;
        message += `💰 <b>Số coin:</b> ${coinAmount}\n`;
        message += `👤 <b>Người gửi:</b> ${displayName}\n`;
         message += `💰 <b>Số tiền:</b> ${transactionData?.estimated_vnd}\n`;
        message += `🆔 <b>Steam ID:</b> <code>${steamId}</code>\n`;
        message += `📅 <b>Thời gian:</b> ${transactionDate}\n`;
        message += `🔗 <b>Transaction ID:</b> <code>${transactionId}</code>\n\n`;
        message += `🔍 <b>Chưa tìm thấy order khớp</b>\n`;
        message += `⏰ Sẽ tiếp tục kiểm tra trong 5 phút tới...`;

        return await sendTelegramMessage(message, {
            parseMode: 'HTML',
            disableNotification: false
        });

    } catch (error) {
        console.error('Error sending transaction pending notification:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Send notification about order found for pending transaction
 * @param {Object} transactionData - Transaction data
 * @param {Object} orderData - Matching buycoin order data
 * @returns {Promise<Object>} Send result
 */
export async function sendOrderFoundNotification(transactionData, orderData) {
    try {
        const coinAmount = transactionData.delta_coins || (transactionData.delta_amount / 100);
        const steamId = transactionData.steam_id
        const transactionId = transactionData.transaction_id;

        const orderInfo = {
            orderId: orderData.order_id,
            expectedCoin: orderData.coin_amount,
            vndAmount: orderData.vnd_amount,
            bankCode: orderData.bank_code,
            accountNumber: orderData.account_number,
            accountName: orderData.account_name,
            createdAt: orderData.created_at
        };

        let message = `✅ <b>TÌM THẤY ORDER CHO TRANSACTION!</b>\n\n`;
        message += `💰 <b>Số coin:</b> ${coinAmount}\n`;
        message += `🆔 <b>Steam ID:</b> <code>${steamId}</code>\n`;
        message += `🔗 <b>Transaction ID:</b> <code>${transactionId}</code>\n\n`;
        message += `📋 <b>THÔNG TIN ORDER:</b>\n`;
        message += `📋 <b>Mã đơn:</b> <code>${orderInfo.orderId}</code>\n`;
        message += `💎 <b>Coin mong đợi:</b> ${orderInfo.expectedCoin}\n`;
        message += `💵 <b>Số tiền VND:</b> ${orderInfo.vndAmount.toLocaleString('vi-VN')} VND\n`;
        message += `🏦 <b>Ngân hàng:</b> ${orderInfo.bankCode}\n`;
        message += `💳 <b>STK:</b> <code>${orderInfo.accountNumber}</code>\n`;
        message += `👤 <b>Chủ TK:</b> ${orderInfo.accountName || 'Chưa xác định'}\n`;
        message += `⏰ <b>Tạo đơn lúc:</b> ${new Date(orderInfo.createdAt).toLocaleString('vi-VN')}\n\n`;

        // Check if coin amounts match
        const expectedCoin = parseFloat(orderInfo.expectedCoin);
        const receivedCoin = parseFloat(coinAmount);

        if (Math.abs(expectedCoin - receivedCoin) < 0.01) {
            message += `✅ <b>Số coin khớp chính xác!</b>\n`;
            message += `🔄 <b>Order đã được chuyển sang trạng thái PROCESSING</b>`;
        } else {
            message += `⚠️ <b>Số coin không khớp!</b>\n`;
            message += `Expected: ${expectedCoin}, Received: ${receivedCoin}`;
        }

        return await sendTelegramMessage(message, {
            parseMode: 'HTML',
            disableNotification: false
        });

    } catch (error) {
        console.error('Error sending order found notification:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Send notification about transaction expired (no order found after 5 minutes)
 * @param {Object} transactionData - Transaction data
 * @returns {Promise<Object>} Send result
 */
export async function sendTransactionExpiredNotification(transactionData) {
    try {
        const coinAmount = transactionData.delta_coins || (transactionData.delta / 100);
        const steamId = transactionData.data?.steam_id;
        const displayName = transactionData.data?.display_name || 'Unknown';
        const transactionId = transactionData.id;
        const transactionDate = transactionData.date;

        let message = `❌ <b>TRANSACTION ĐÃ HẾT HẠN!</b>\n\n`;
        message += `💰 <b>Số coin:</b> ${coinAmount}\n`;
        message += `🆔 <b>Steam ID:</b> <code>${steamId}</code>\n`;
        message += `📅 <b>Thời gian:</b> ${transactionDate}\n`;
        message += `🔗 <b>Transaction ID:</b> <code>${transactionId}</code>\n\n`;
        message += `⏰ <b>Đã quá 5 phút không tìm thấy order khớp</b>\n`;

        return await sendTelegramMessage(message, {
            parseMode: 'HTML',
            disableNotification: false
        });

    } catch (error) {
        console.error('Error sending transaction expired notification:', error);
        return {
            success: false,
            error: error.message
        };
    }
}
