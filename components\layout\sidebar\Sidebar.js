import { But<PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>lose, SheetHeader, SheetTitle, SheetTrigger, } from "@/components/ui/sheet"
import { Menu } from 'lucide-react';
import NavbarLogo from "@/components/layout/header/Logo";
import { UsermenuMobile } from "@/components/layout/sidebar/MenuUser"
import Footer from "@/components/layout/sidebar/Footer";
import { ScrollArea } from "@/components/ui/scroll-area"
import { ModeToggle } from "@/components/layout/header/ModeToggle";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTrigger,DialogTitle } from '@/components/ui/dialog';
import Link from "next/link";
import LoginForm from "@/components/features/auth/LoginForm";



export default function Sidebar({ isLoggedIn, user }) {
    return (
        <Sheet>
            <div className="flex items-center border-l border-slate-100 ml-6 pl-6 dark:border-slate-600">
                <div className="md:hidden">
                    <ModeToggle />
                </div>
                <SheetTrigger asChild>
                    <Button variant="ghost" className="md:hidden">
                        <Menu />
                    </Button>
                </SheetTrigger>
            </div>
            <SheetContent side="right" className="w-full p-0">
                <SheetHeader withSheetClose>
                    <SheetTitle>
                        <div className='p-4 border-b border-slate-900/10 lg:border-0 dark:border-slate-300/10'>
                            <SheetClose asChild>
                                <Link href={"/"}>
                                    <NavbarLogo />
                                </Link>
                            </SheetClose>
                        </div>
                    </SheetTitle>

                </SheetHeader>
                <MenuU withSheetClose isLoggedIn={isLoggedIn} user={user} />
                <Footer />
            </SheetContent>
        </Sheet>
    )
}

const MenuU = (props) => {
    const LINKS = [
        { name: "Trang chủ", href: "/" },
        { name: "EtopFun", href: "/etopfun" },
        { name: "Hỗ trợ", href: "/" },
    ];
    return (
        <ScrollArea className="h-4/5 w-full p-3">
            <div className='flex flex-col'>
                <nav>
                    <ul className="flex flex-col space-y-2">
                        {LINKS.map(link => (
                            <li key={link.name} className='w-full font-semibold leading-6 text-slate-700 dark:text-slate-200 hover:bg-sky-500/10'>
                                <SheetClose asChild>
                                    <Link
                                        className="block w-full py-2 px-5 hover:text-sky-400"
                                        href={link.href}
                                    >
                                        {link.name}
                                    </Link>
                                </SheetClose>
                            </li>
                        ))}
                    </ul>
                </nav>

                {!props.isLoggedIn && (
                    <SheetClose asChild>
                        <Dialog>
                            <DialogTrigger>
                                <div className="mr-2 h-12 mt-2" variant="ghost">Đăng nhập</div>
                            </DialogTrigger>
                            <DialogContent className='sm:max-w-[425px]'>
                                <DialogHeader>
                                    <DialogTitle>Đăng nhập</DialogTitle>
                                    <DialogDescription>
                                        Vui lòng chọn phương thức đăng nhập
                                    </DialogDescription>
                                </DialogHeader>
                                <LoginForm></LoginForm>
                            </DialogContent>
                        </Dialog>
                    </SheetClose>
                )}

                {props.isLoggedIn && (
                    <SheetClose asChild>
                        <div>
                            <UsermenuMobile user={props.user} />
                        </div>
                    </SheetClose>
                )}
            </div>
        </ScrollArea>
    )
}