import { But<PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetClose, SheetHeader, Sheet<PERSON><PERSON>le, Sheet<PERSON>rigger, } from "@/components/ui/sheet"
import { Menu } from 'lucide-react';
import NavbarLogo from "@/components/layout/header/Logo";
import { UsermenuMobile } from "@/components/layout/sidebar/MenuUser"
import Footer from "@/components/layout/sidebar/Footer";
import { ScrollArea } from "@/components/ui/scroll-area"
import { ModeToggle } from "@/components/layout/header/ModeToggle";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTrigger,DialogTitle } from '@/components/ui/dialog';
import Link from "next/link";
import LoginForm from "@/components/features/auth/LoginForm";



export default function Sidebar({ isLoggedIn, user }) {
    return (
        <div className="md:hidden">
            <Sheet>
                <div className="flex items-center border-l border-slate-100 ml-6 pl-6 dark:border-slate-600">
                    <div className="mr-3">
                        <ModeToggle />
                    </div>
                    <SheetTrigger asChild>
                        <Button variant="ghost" size="sm" className="p-2">
                            <Menu className="h-5 w-5" />
                            <span className="sr-only">Mở menu</span>
                        </Button>
                    </SheetTrigger>
                </div>
                <SheetContent side="right" className="w-full p-0 flex flex-col bg-background/80 backdrop-blur-md border-l border-border/30">
                    <SheetHeader className="flex-shrink-0">
                        <SheetTitle>
                            <div className='p-4 border-b border-border/20 bg-background/50 backdrop-blur-sm'>
                                <SheetClose asChild>
                                    <Link href={"/"} className="block">
                                        <NavbarLogo />
                                    </Link>
                                </SheetClose>
                            </div>
                        </SheetTitle>
                    </SheetHeader>

                    <div className="flex-1 flex flex-col min-h-0">
                        <MenuU withSheetClose isLoggedIn={isLoggedIn} user={user} />
                        <div className="mt-auto flex-shrink-0">
                            <Footer />
                        </div>
                    </div>
                </SheetContent>
            </Sheet>
        </div>
    )
}

const MenuU = (props) => {
    const LINKS = [
        { name: "Trang chủ", href: "/" },
        { name: "EtopFun", href: "/etopfun" },
    ];
    return (
        <ScrollArea className="flex-1 w-full overflow-y-auto">
            <div className='flex flex-col p-4 space-y-4 min-h-full'>
                <nav>
                    <ul className="flex flex-col space-y-1">
                        {LINKS.map(link => (
                            <li key={link.name} className='w-full'>
                                <SheetClose asChild>
                                    <Link
                                        className="block w-full py-3 px-4 rounded-lg font-medium text-foreground/90 dark:text-slate-200 hover:bg-primary/10 hover:text-primary transition-colors backdrop-blur-sm"
                                        href={link.href}
                                    >
                                        {link.name}
                                    </Link>
                                </SheetClose>
                            </li>
                        ))}
                    </ul>
                </nav>

                <div className="mt-6 pt-4 border-t border-border/20">
                    {!props.isLoggedIn && (
                        <Dialog>
                            <DialogTrigger asChild>
                                <Button variant="outline" className="w-full backdrop-blur-sm">
                                    Đăng nhập
                                </Button>
                            </DialogTrigger>
                            <DialogContent className='sm:max-w-[425px]'>
                                <DialogHeader>
                                    <DialogTitle>Đăng nhập</DialogTitle>
                                    <DialogDescription>
                                        Vui lòng chọn phương thức đăng nhập
                                    </DialogDescription>
                                </DialogHeader>
                                <LoginForm />
                            </DialogContent>
                        </Dialog>
                    )}

                    {props.isLoggedIn && (
                        <UsermenuMobile user={props.user} />
                    )}
                </div>
            </div>
        </ScrollArea>
    )
}