"use client";
import { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  User,
  ChevronDown,
  Plus,
  Trash2,
  Check,
  Settings
} from "lucide-react";

export default function SteamIdSelector({
  steamIds,
  onAddSteamId,
  onRemoveSteamId,
  register,
  errors,
  state,
  defaultValue
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState(defaultValue || '');
  const [showAddNew, setShowAddNew] = useState(false);
  const [autoSave, setAutoSave] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const timeoutRef = useRef(null);

  // Auto save function
  const autoSaveSteamId = (steamId) => {
    if (autoSave && steamId && steamId.trim() && !steamIds.includes(steamId.trim())) {
      onAddSteamId(steamId.trim());
    }
  };

  // Clear timeout when component unmounts or input changes
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleSelectSteamId = (steamId) => {
    setInputValue(steamId);
    setIsOpen(false);
    // Trigger form update
    const steamIdInput = document.getElementById('steamId');
    if (steamIdInput) {
      steamIdInput.value = steamId;
      steamIdInput.dispatchEvent(new Event('input', { bubbles: true }));
    }
  };

  const handleAddNew = () => {
    if (inputValue && !steamIds.includes(inputValue)) {
      onAddSteamId(inputValue);
      setShowAddNew(false);
    }
  };

  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);

    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new timeout for auto save (delay 2 seconds)
    if (autoSave && value.trim()) {
      timeoutRef.current = setTimeout(() => {
        autoSaveSteamId(value);
      }, 2000);
    }
  };

  const handleInputBlur = () => {
    // Auto save on blur if enabled
    if (autoSave && inputValue.trim()) {
      // Clear timeout and save immediately
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      autoSaveSteamId(inputValue);
    }
  };

  const handleRemove = (steamId, e) => {
    e.stopPropagation();
    if (confirm(`Xóa Steam ID "${steamId}" khỏi danh sách?`)) {
      onRemoveSteamId(steamId);
      if (inputValue === steamId) {
        setInputValue('');
      }
    }
  };

  return (
    <div className="space-y-2 md:space-y-3">
      <Label htmlFor="steamId" className="flex items-center space-x-2 text-base font-medium">
        <User className="h-4 w-4 text-orange-600" />
        <span>Steam ID (Chuyển Coin)</span>
      </Label>
      
      <div className="flex space-x-2">
        {/* Steam ID Input */}
        <div className="flex-1 relative">
          <Input
            id="steamId"
            type="text"
            {...register("steamId")}
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            placeholder={autoSave ? "Nhập Steam ID (tự động lưu)" : "Nhập Steam ID để chuyển coin"}
            className="h-12 pl-4 pr-10 text-lg border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 transition-all duration-200"
          />
          
          {/* Dropdown Button */}
          {steamIds.length > 0 && (
            <button
              type="button"
              onClick={() => setIsOpen(!isOpen)}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-orange-600 transition-colors"
            >
              <ChevronDown className="h-5 w-5" />
            </button>
          )}

          {/* Dropdown List */}
          {isOpen && steamIds.length > 0 && (
            <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white dark:bg-slate-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-48 overflow-y-auto">
              {steamIds.map((steamId) => (
                <div
                  key={steamId}
                  onClick={() => handleSelectSteamId(steamId)}
                  className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                >
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    <span className="font-mono text-sm text-gray-900 dark:text-white truncate">
                      {steamId}
                    </span>
                    {inputValue === steamId && (
                      <Check className="h-4 w-4 text-orange-600 flex-shrink-0" />
                    )}
                  </div>
                  <button
                    onClick={(e) => handleRemove(steamId, e)}
                    className="ml-2 p-1 text-gray-400 hover:text-red-600 transition-colors flex-shrink-0"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Settings Button */}
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setShowSettings(!showSettings)}
          className="px-3 h-12"
          title="Cài đặt tự động lưu"
        >
          <Settings className="h-4 w-4" />
        </Button>

        {/* Add Button - Only show when auto save is disabled */}
        {!autoSave && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setShowAddNew(!showAddNew)}
            className="px-3 h-12"
            title="Lưu Steam ID này"
          >
            <Plus className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="p-3 bg-gray-50 dark:bg-gray-900/20 rounded-lg border border-gray-200 dark:border-gray-800">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Tự động lưu Steam ID</span>
              <Button
                type="button"
                variant={autoSave ? "default" : "outline"}
                size="sm"
                onClick={() => setAutoSave(!autoSave)}
              >
                {autoSave ? "Bật" : "Tắt"}
              </Button>
            </div>
            {autoSave && (
              <p className="text-xs text-gray-600 dark:text-gray-400">
                Steam ID sẽ được tự động lưu sau 2 giây hoặc khi rời khỏi ô nhập
              </p>
            )}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(false)}
              className="w-full"
            >
              Đóng
            </Button>
          </div>
        </div>
      )}

      {/* Add New Steam ID - Only show when auto save is disabled */}
      {!autoSave && showAddNew && (
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="flex space-x-2">
            <Button
              type="button"
              size="sm"
              onClick={handleAddNew}
              disabled={!inputValue || steamIds.includes(inputValue)}
              className="flex-1"
            >
              Lưu Steam ID này
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowAddNew(false)}
            >
              Hủy
            </Button>
          </div>
        </div>
      )}

      {/* Error Messages */}
      {errors.steamId && (
        <p className="text-sm text-red-500 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{errors.steamId.message}</span>
        </p>
      )}
      {state?.errors?.steamId && (
        <p className="text-sm text-red-500 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{state.errors.steamId[0]}</span>
        </p>
      )}

      {/* Saved Steam IDs Info */}
      {steamIds.length > 0 && (
        <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
          <div>Đã lưu {steamIds.length} Steam ID</div>
          {autoSave && (
            <div className="text-green-600 dark:text-green-400">
              ✓ Tự động lưu đang bật
            </div>
          )}
        </div>
      )}
    </div>
  );
}
