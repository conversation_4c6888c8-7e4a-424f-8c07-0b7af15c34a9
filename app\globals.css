@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=JetBrains+Mono:wght@400;500;600;700&display=swap');

/* Enhanced Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar-track {
  background: linear-gradient(to bottom, #f8fafc, #e2e8f0);
  border-radius: 12px;
  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.1);
}

.custom-scrollbar::-webkit-scrollbar {
  width: 12px;
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #ec4899);
  border-radius: 12px;
  border: 2px solid transparent;
  background-clip: content-box;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #2563eb, #7c3aed, #db2777);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.5);
}

/* Dark mode scrollbar */
.dark .custom-scrollbar::-webkit-scrollbar-track {
  background: linear-gradient(to bottom, #1e293b, #334155);
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #60a5fa, #a78bfa, #f472b6);
}

/* Enhanced Typography */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Monospace font for code and numbers */
.font-mono {
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Cascadia Code', monospace;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Enhanced gradient text utilities */
  .gradient-text-primary {
    @apply bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent;
  }

  .gradient-text-secondary {
    @apply bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 bg-clip-text text-transparent;
  }

  /* Glass morphism utilities */
  .glass {
    @apply backdrop-blur-xl bg-white/10 dark:bg-slate-900/10 border border-white/20 dark:border-slate-700/20;
  }

  .glass-strong {
    @apply backdrop-blur-2xl bg-white/20 dark:bg-slate-900/20 border border-white/30 dark:border-slate-700/30;
  }

  /* Enhanced shadow utilities */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3), 0 0 40px rgba(139, 92, 246, 0.2);
  }

  .shadow-glow-purple {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.4), 0 0 40px rgba(236, 72, 153, 0.2);
  }

  /* Animation utilities */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 4s ease infinite;
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}



@layer base {
  :root {
    /* Enhanced Light Theme Colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Enhanced Primary Colors - Blue to Purple Gradient */
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --primary-hover: 221.2 83.2% 48%;

    /* Enhanced Secondary Colors */
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --secondary-hover: 210 40% 94%;

    /* Enhanced Muted Colors */
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* Enhanced Accent Colors */
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --accent-hover: 210 40% 92%;

    /* Enhanced Status Colors */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 222.2 84% 4.9%;

    /* Enhanced Border and Input */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    /* Enhanced Radius */
    --radius: 0.75rem;

    /* Enhanced Chart Colors */
    --chart-1: 221 83% 53%;
    --chart-2: 262 83% 58%;
    --chart-3: 142 76% 36%;
    --chart-4: 38 92% 50%;
    --chart-5: 0 84% 60%;

    /* Custom Brand Colors */
    --brand-blue: 221.2 83.2% 53.3%;
    --brand-purple: 262.1 83.3% 57.8%;
    --brand-gradient: linear-gradient(135deg, hsl(var(--brand-blue)), hsl(var(--brand-purple)));
  }

  .dark {
    /* Enhanced Dark Theme Colors */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    /* Enhanced Primary Colors for Dark Mode */
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --primary-hover: 217.2 91.2% 65%;

    /* Enhanced Secondary Colors for Dark Mode */
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --secondary-hover: 217.2 32.6% 22%;

    /* Enhanced Muted Colors for Dark Mode */
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    /* Enhanced Accent Colors for Dark Mode */
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --accent-hover: 217.2 32.6% 22%;

    /* Enhanced Status Colors for Dark Mode */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --success: 142 71% 45%;
    --success-foreground: 210 40% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 222.2 84% 4.9%;

    /* Enhanced Border and Input for Dark Mode */
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    /* Enhanced Chart Colors for Dark Mode */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Custom Brand Colors for Dark Mode */
    --brand-blue: 217.2 91.2% 59.8%;
    --brand-purple: 262.1 83.3% 67.8%;
  }
}


@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    /* Enhanced smooth transitions */
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Enhanced heading styles */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
    line-height: 1.2;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  h3 {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  h4 {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  /* Enhanced link styles */
  a {
    @apply transition-colors duration-200;
  }

  /* Enhanced button base styles */
  button {
    @apply transition-all duration-200 ease-in-out;
  }

  /* Enhanced input base styles */
  input, textarea, select {
    @apply transition-all duration-200 ease-in-out;
  }

  /* Enhanced focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2 ring-offset-background;
  }

  /* Enhanced selection styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }

  /* Enhanced scrollbar for all elements */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--primary)) hsl(var(--muted));
  }
}

@keyframes float-animation {
  0% {
    opacity: 0;
    transform: translateY(0) rotate(0deg) scale(1);
  }
  30% {
    opacity: 0.4;
    transform: translateY(-20px) rotate(180deg) scale(1.05);
  }
  100% {
    opacity: 0;
    transform: translateY(0) rotate(360deg) scale(1);
  }
}

.floating-element {
  will-change: transform, opacity;
  animation: float-animation infinite ease-in-out;
}