import { NextResponse } from 'next/server';
import { execute } from '@/lib/db';

// GET - Get all transactions with statistics
export async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const status = searchParams.get('status');
        const limit = parseInt(searchParams.get('limit')) || 100;
        const offset = parseInt(searchParams.get('offset')) || 0;

        // Build WHERE clause
        let whereClause = '';
        let queryParams = [];
        
        if (status && status !== 'all') {
            whereClause = 'WHERE status = ?';
            queryParams.push(status);
        }

        // Get transactions
        const transactionsQuery = `
            SELECT 
                id,
                transaction_id,
                steam_id,
                coin_amount,
                delta_amount,
                transaction_type,
                transaction_key,
                transaction_timestamp,
                transaction_date,
                estimated_vnd,
                matched_order_id,
                status,
                first_detected_at,
                last_checked_at,
                check_count,
                order_found,
                order_found_at,
                expired,
                expired_at,
                notification_sent,
                notification_sent_at,
                processed_at,
                created_at,
                updated_at
            FROM transaction_tracking 
            ${whereClause}
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        `;

        const [transactions] = await execute(transactionsQuery, [...queryParams, limit, offset]);

        // Get statistics
        const statsQuery = `
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'processed' THEN 1 ELSE 0 END) as processed,
                SUM(CASE WHEN status = 'matched' THEN 1 ELSE 0 END) as matched,
                SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                SUM(CASE WHEN matched_order_id IS NOT NULL THEN 1 ELSE 0 END) as with_orders,
                SUM(CASE WHEN notification_sent = 1 THEN 1 ELSE 0 END) as notifications_sent,
                AVG(coin_amount) as avg_coin_amount,
                SUM(coin_amount) as total_coins
            FROM transaction_tracking
        `;

        const [statsResult] = await execute(statsQuery);
        const stats = statsResult[0];

        // Get recent activity (last 24 hours)
        const recentActivityQuery = `
            SELECT 
                COUNT(*) as recent_total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as recent_pending,
                SUM(CASE WHEN status = 'matched' THEN 1 ELSE 0 END) as recent_matched,
                SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as recent_expired
            FROM transaction_tracking 
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        `;

        const [recentActivityResult] = await execute(recentActivityQuery);
        const recentActivity = recentActivityResult[0];

        // Get pending transactions that are close to expiring (4+ minutes old)
        const expiringQuery = `
            SELECT COUNT(*) as expiring_soon
            FROM transaction_tracking 
            WHERE status = 'pending' 
            AND first_detected_at <= DATE_SUB(NOW(), INTERVAL 4 MINUTE)
            AND first_detected_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        `;

        const [expiringResult] = await execute(expiringQuery);
        const expiringSoon = expiringResult[0].expiring_soon;

        return NextResponse.json({
            success: true,
            transactions,
            stats: {
                ...stats,
                expiring_soon: expiringSoon,
                recent_activity: recentActivity
            },
            pagination: {
                limit,
                offset,
                total: stats.total
            }
        });

    } catch (error) {
        console.error('Error fetching transactions:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to fetch transactions'
        }, { status: 500 });
    }
}

// POST - Manual actions on transactions
export async function POST(request) {
    try {
        const body = await request.json();
        const { action, transactionId, data } = body;

        switch (action) {
            case 'mark_expired':
                await execute(`
                    UPDATE transaction_tracking 
                    SET status = 'expired', 
                        expired = TRUE, 
                        expired_at = NOW(),
                        updated_at = NOW()
                    WHERE transaction_id = ? AND status = 'pending'
                `, [transactionId]);
                break;

            case 'mark_cancelled':
                await execute(`
                    UPDATE transaction_tracking 
                    SET status = 'cancelled',
                        updated_at = NOW()
                    WHERE transaction_id = ?
                `, [transactionId]);
                break;

            case 'retry_notification':
                await execute(`
                    UPDATE transaction_tracking 
                    SET notification_sent = FALSE,
                        notification_sent_at = NULL,
                        updated_at = NOW()
                    WHERE transaction_id = ?
                `, [transactionId]);
                break;

            case 'manual_match':
                if (!data.orderId) {
                    return NextResponse.json({
                        success: false,
                        error: 'Order ID is required for manual match'
                    }, { status: 400 });
                }

                await execute(`
                    UPDATE transaction_tracking 
                    SET status = 'matched',
                        matched_order_id = ?,
                        order_found = TRUE,
                        order_found_at = NOW(),
                        updated_at = NOW()
                    WHERE transaction_id = ?
                `, [data.orderId, transactionId]);
                break;

            default:
                return NextResponse.json({
                    success: false,
                    error: 'Invalid action'
                }, { status: 400 });
        }

        return NextResponse.json({
            success: true,
            message: `Action ${action} completed successfully`
        });

    } catch (error) {
        console.error('Error performing transaction action:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to perform action'
        }, { status: 500 });
    }
}

// DELETE - Clean up old transactions
export async function DELETE(request) {
    try {
        const { searchParams } = new URL(request.url);
        const olderThanDays = parseInt(searchParams.get('days')) || 7;

        const deleteQuery = `
            DELETE FROM transaction_tracking 
            WHERE status IN ('expired', 'cancelled') 
            AND created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        `;

        const [result] = await execute(deleteQuery, [olderThanDays]);

        return NextResponse.json({
            success: true,
            message: `Cleaned up ${result.affectedRows} old transactions`,
            deletedCount: result.affectedRows
        });

    } catch (error) {
        console.error('Error cleaning up transactions:', error);
        return NextResponse.json({
            success: false,
            error: 'Failed to clean up transactions'
        }, { status: 500 });
    }
}
