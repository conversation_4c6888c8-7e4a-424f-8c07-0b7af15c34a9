'server-only'
import jwt from 'jsonwebtoken';
const JWT_SECRET = process.env.JWT_SECRET;
const SECRET_KEY = process.env.SECRET_KEY;
import { jwtVerify } from 'jose';
export  function generateToken(data) {
  return jwt.sign(data, JWT_SECRET, { expiresIn: '15m' });
}

export  function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (err) {
    return null;
  }
}
export async function getPayloadIfTokenValidatedEdge(token) {
  try {
    const key = await generateHS512KeyWithSecret()
    const decodedToken = await jwtVerify(token, key);
   
    const payload = decodedToken.payload;
    
    return payload
  } catch (error) {
    console.log(error.message)
    return null
  }
}

export async function generateHS512KeyWithSecret() {
  let algorithm = { name: "HMAC", hash: "SHA-512" };
  let secretKeyData = Buffer.from(SECRET_KEY, 'base64')
  const key = await crypto.subtle.importKey(
    "raw",
    secretKeyData,
    algorithm,
    true,
    ["verify"]
  );
  return key;
}