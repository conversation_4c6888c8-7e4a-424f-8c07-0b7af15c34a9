'use client'
import { logout } from "@/actions/auth"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { LogOut } from "lucide-react"
export default function LogoutButton() {
    return (
        <Button onClick={async () => {
            await logout()
        }} variant="ghost" className="w-full px-0 py-0 justify-start text-red-500 hover:text-red-600 hover:bg-red-50">
            <LogOut className="mr-2 h-4 w-4" />
            Đăng xuất
        </Button>
    )

}