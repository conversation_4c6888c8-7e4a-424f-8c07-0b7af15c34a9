

import OrderTabs from "@/components/features/common/OrderTabs"
import { fetchHome, fetchLevelUser, fetchUserInfo } from "@/lib/http";
import { checkAuth } from "@/lib/utils";
import SimpleCard from "@/components/ui/simple-card";
import MaintenanceNotice from "@/components/ui/maintenance-notice";
import TransparentNotice from "@/components/ui/transparent-notice";
export const metadata = {
  title: 'EmpireVn.store -Auto bán coin csgoempire.com ',
  description: 'Web bán coin csgoempire.com tự động',
  keywords: "csgoempire csgo csgoempirevn empirevn empirevn.store, item csgo, skin csgo"
}
import { cookies } from "next/headers";
import CoinDisplay from "@/components/ui/coin-display";
import CopyId from "@/components/ui/copy-id";
export default async function Page() {
  try {
    const cookieStore = cookies()
    let steamId = cookieStore.get('steamId')?.value || ''
    const [data, authResult] = await Promise.all([
      fetchHome(),
      checkAuth(cookieStore)
    ])


    if (!data) return (
      <div className="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
        <MaintenanceNotice
          isVisible={true}
          message="Hệ thống đang trong quá trình bảo trì định kỳ. Một số tính năng có thể tạm thời không khả dụng. Chúng tôi sẽ hoàn thành sớm nhất có thể."
          type="warning"
        />
      </div>
    )
    const { isLoggedIn } = authResult

    const { banks, coinRate, coinBuyRate, coinBalance, coinRateUser } = data
    let rate = coinRate;
    if (isLoggedIn) {
      rate = coinRateUser
      const [level, userInfo] = await Promise.all([
        fetchLevelUser().catch(err => {
          console.warn('Failed to fetch user level:', err)
          return null
        }),
        fetchUserInfo().catch(err => {
          console.warn('Failed to fetch user info:', err)
          return null
        })
      ])

      if (level && level?.id) {
        let rateLevel = level.rates.find(r => r.coinName == "csgoempire")
        if (rateLevel) rate = rateLevel.value
      }
      if (userInfo?.steamId) {
        steamId = userInfo.steamId
      } else if (userInfo?.lastSteamId) {
        steamId = userInfo.lastSteamId
      }

    }
    let settings = data.settings
    const notificationSetting = settings.find(setting => setting.name === "NOTIFICATION");
    const facebookSetting = settings.find(setting => setting.name === "FACEBOOK");
    return (
      <>

        <div className=" ">
          {/* Transparent Notice */}
          {notificationSetting && notificationSetting.value && (
            <div className="max-w-8xl mx-auto px-1 md:px-6 lg:px-8 pt-4">
              <TransparentNotice
                isVisible={true}
                title="Thông báo"
                message={notificationSetting.value}
                type="announcement"
                closeable={true}
              />
            </div>
          )}

          {/* Hero Section */}
          <div className="max-w-8xl mx-auto px-1 md:px-6 lg:px-8 pt-2 pb-6">
            {/* Main Trading Section */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 ">
              {/* Available Coins Card */}
              <SimpleCard className="lg:col-span-1 p-1 md:p-6">
                <div className="text-center">
                  <CoinDisplay coinBalance={coinBalance} />
                  <div className="mt-2 md:mt-6 space-y-3">
                    <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-slate-700 rounded-lg">
                      <span className="text-gray-600 dark:text-gray-400">Tỷ giá bán:</span>
                      <span className="font-semibold text-green-600 dark:text-green-400">
                        {(rate * 1000).toLocaleString()} VND/Coin
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-slate-700 rounded-lg">
                      <span className="text-gray-600 dark:text-gray-400">Tỷ giá mua:</span>
                      <span className="font-semibold text-blue-600 dark:text-blue-400">
                        {(coinBuyRate * 1000).toLocaleString()} VND/Coin
                      </span>
                    </div>


                  </div>
                </div>
                <div className=" bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 mt-6 space-y-3 p-3  rounded-lg">
                  <div className="text-gray-600 dark:text-gray-400">ID nhận coin csgoempire:</div>
                  <CopyId />
                </div>
              </SimpleCard>

              {/* Order Tabs */}
              <SimpleCard className="lg:col-span-2 md:p-6 p-0">
                <OrderTabs
                  banks={banks}
                  rateBuy={coinBuyRate}
                  rateSell={rate}
                  availableCoins={coinBalance}
                  steamId={steamId}
                  facebook={facebookSetting}
                />
              </SimpleCard>
            </div>
          </div>
        </div>
      </>

    )
  } catch (error) {
    <div className="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
      <MaintenanceNotice
        isVisible={true}
        message="Hệ thống đang trong quá trình bảo trì định kỳ. Một số tính năng có thể tạm thời không khả dụng. Chúng tôi sẽ hoàn thành sớm nhất có thể."
        type="warning"
      />
    </div>
  }
}