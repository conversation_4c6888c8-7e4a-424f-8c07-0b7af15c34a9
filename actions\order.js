"use server";
'server-only'
import { orderSchema } from "@/validations/order.schema";
import { fetchHome, fetchOrder, fetchOrderEtop, fetchOrderStatus, fetchPreTip, fetchUserEtopfun } from "@/lib/http";
import { cookies } from 'next/headers'
import { settingCookies } from "@/lib/utils";
import { orderSchemaEtop } from "@/validations/orderEtopfun.schema";
export async function order(prevState, data) {
   
    const home = await fetchHome()
    const { banks } = home
    if (!(data instanceof FormData)) return {
        success: false
    }
    const parseResult = orderSchema.safeParse(Object.fromEntries(data.entries()))
    if (!parseResult.success) {
        return {
            errors: parseResult.error.flatten().fieldErrors,
        }
    }
    let { amount, steamId, bankNo, nickName, coin } = Object.fromEntries(data.entries())
    const bank = banks.find(b => b.bankNo === bankNo);
    if (!bank) {
        return {
            errorServer: true,
            message: "Lỗi ngân hàng thanh toán"
        };
    }
    coin = coin * 100
    let response = await fetchOrder({ amount, steamId, bankNo, coin, bankType: bank.bankType })
    if (!response.error) {
        const { bank, orderId, steamAvatar, steamId, steamLevel, steamName, content, coin, rate , amount} = response
        cookies().set('steamId', steamId, settingCookies)
        return {
            success: true,
            data: { bank, orderId, steamAvatar, steamId, steamLevel, steamName, content, coin, rate, amount },
        }
    }
    return {
        errorServer: true,
        message: response?.error
    };


}
export async function checkOrderStatus(orderId) {
    return fetchOrderStatus(orderId);
}
export async function orderEtopfun(selectedItems, prevState, data) {
    if (!(data instanceof FormData)) return {
        success: false
    }
    const parseResult = orderSchemaEtop.safeParse(Object.fromEntries(data.entries()))
    if (!parseResult.success) {
        return {
            fieldErrors: parseResult.error.flatten().fieldErrors,
        };
    }

    let { steamId, nickName, bankNo } = Object.fromEntries(data.entries())
    let dataUser = await fetchUserEtopfun(steamId)
    if (!dataUser || dataUser.name != nickName) {
        return {
            message: "Tài khoản không tồn tại.",
            errorServer: true,
        };
    }
    steamId = dataUser.steam_id
    nickName = dataUser.name
    const home = await fetchHome()
    const { banks, coins } = home
    const coinEtop = coins.find(setting => setting.name === "ETOP");
    const bank = banks.find(b => b.bankNo === bankNo);
    if (!bank) {
        return {
            errorServer: true,
            message: "Ngân hàng không hỗ trợ",
        };
    }
    const dataPreTip = await fetchPreTip({
        steam_id: steamId,
        items: selectedItems
    })
    if (!dataPreTip)
        return {
            message: "Items không tồn tại vui lòng F5 và thử lại",
            errorServer: true,
        };
    selectedItems = dataPreTip;
    let totalValue = selectedItems.reduce((sum, item) => sum + item.value, 0)
    let amount = 0;
    const coin100= totalValue*100
    if (totalValue < 10) amount = coin100 * coinEtop.rate * 10
    else amount = Math.round(coin100 * coinEtop.rate/100.0) * 1000
    let response = await fetchOrderEtop({
        items: selectedItems,
        steamName: nickName,
        money: amount,
        value: totalValue,
        steamId,
        bankType: bank.bankType,
        bankNo
    })
    if (!response.error) {
        const { bank, orderId, steamAvatar, steamId, steamLevel, steamName, content, coin, rate, amount } = response
        cookies().set('steamIdEtop', steamId, settingCookies)
        return {
            success: true,
            data: { bank, orderId, steamAvatar, steamId, steamLevel, steamName, content, coin, rate ,amount},
        }
    }
    return {
        errorServer: true,
        message: response?.error
    };

}