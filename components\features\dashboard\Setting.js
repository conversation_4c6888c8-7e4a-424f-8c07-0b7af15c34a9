
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
export default function Settings() {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Cài đặt</CardTitle>
                <CardDescription>Cài đặt tài khoản</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <Label htmlFor="email-notifications">Thông báo email</Label>
                        <Switch id="email-notifications" />
                    </div>
                    <div className="flex items-center justify-between">
                        <Label htmlFor="marketing-emails">Thông báo steam</Label>
                        <Switch id="marketing-emails" />
                    </div>
                    <div className="flex items-center justify-between">
                        <Label htmlFor="two-factor-auth"><PERSON><PERSON><PERSON> thực 2 lớp</Label>
                        <Switch id="two-factor-auth" />
                    </div>
                    <Button>Lưu cài đặt</Button>
                </div>
            </CardContent>
        </Card>
    )
}