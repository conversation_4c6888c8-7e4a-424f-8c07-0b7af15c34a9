"use client";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Building2 } from "lucide-react";

export default function BankSelector({
  banks,
  selectedBank,
  onBankChange,
  register,
  errors,
  state
}) {
  return (
    <div className="space-y-2 md:space-y-3">
      <Label htmlFor="bankNo" className="flex items-center space-x-2 text-base font-medium">
        <Building2 className="h-4 w-4 text-purple-600" />
        <span>Ngân Hàng Thanh Toán</span>
      </Label>
      <Select {...register("bankNo")} value={selectedBank} onValueChange={onBankChange}>
        <SelectTrigger className="h-12 text-lg border-2 border-gray-200 dark:border-gray-600 focus:border-purple-500 transition-all duration-200">
          <SelectValue placeholder="Chọn ngân hàng để thanh toán" />
        </SelectTrigger>
        <SelectContent>
          {banks.map((bank) => (
            <SelectItem
              disabled={!bank.active}
              key={bank.bankNo}
              value={bank.bankNo}
              className="text-lg py-3"
            >
              <div className="flex items-center space-x-3">
                <Building2 className="h-4 w-4" />
                <span>{bank.bankName}</span>
                {!bank.active && <Badge variant="secondary">Tạm ngưng</Badge>}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {/* Hidden input for bin */}
      <input
        type="hidden"
        {...register("bin")}
        value={banks.find(bank => bank.bankNo === selectedBank)?.bin || ""}
      />

      {errors.bankNo && (
        <p className="text-sm text-red-500 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{errors.bankNo.message}</span>
        </p>
      )}
      {state?.errors?.bankNo && (
        <p className="text-sm text-red-500 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{state.errors.bankNo[0]}</span>
        </p>
      )}
      {errors.bin && (
        <p className="text-sm text-red-500 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{errors.bin.message}</span>
        </p>
      )}
    </div>
  );
}
