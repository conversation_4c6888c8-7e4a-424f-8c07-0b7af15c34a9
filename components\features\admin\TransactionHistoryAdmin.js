'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
    RefreshCw,
    Search,
    Filter,
    Trash2,
    Clock,
    CheckCircle,
    XCircle,
    AlertCircle,
    Coins,
    Activity,
    Bell
} from 'lucide-react';
import SimplePagination from '@/components/ui/SimplePagination';
import Pagination from '@/components/ui/Pagination';

export default function TransactionHistoryAdmin() {
    const [transactions, setTransactions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [lastUpdate, setLastUpdate] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [sourceFilter, setSourceFilter] = useState('all');

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(50);
    const [pagination, setPagination] = useState({
        page: 1,
        limit: 50,
        total: 0,
        totalPages: 0
    });

    // Fetch transaction history with pagination
    const fetchTransactions = async (page = currentPage, limit = itemsPerPage) => {
        setLoading(true);
        setError(null);

        try {
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString()
            });

            const response = await fetch(`/api/admin/coin-monitor/transactions?${params}`);
            const data = await response.json();

            if (data.success) {
                setTransactions(data.data);
                setPagination(data.pagination);
                setCurrentPage(data.pagination.page);
                setLastUpdate(new Date());
              
            } else {
                setError(data.error);
            }
        } catch (err) {
            setError('Failed to fetch transaction history');
            console.error('Error fetching transactions:', err);
        } finally {
            setLoading(false);
        }
    };

    // Cleanup old transactions
    const cleanupOldTransactions = async () => {
        setLoading(true);
        setError(null);

        try {
            const response = await fetch('/api/admin/coin-monitor/transactions', {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                fetchTransactions();
            } else {
                setError(data.error);
            }
        } catch (err) {
            setError('Failed to cleanup old transactions');
            console.error('Error cleaning up transactions:', err);
        } finally {
            setLoading(false);
        }
    };

    // Handle pagination changes
    const handlePageChange = (page) => {
        setCurrentPage(page);
        fetchTransactions(page, itemsPerPage);
    };

    const handleItemsPerPageChange = (newItemsPerPage) => {
        setItemsPerPage(newItemsPerPage);
        setCurrentPage(1); // Reset to first page
        fetchTransactions(1, newItemsPerPage);
    };

    // No need for client-side filtering since we're using server-side pagination

    // Calculate statistics

    const getStatusBadgeVariant = (status) => {
        switch (status) {
            case 'pending': return 'secondary';
            case 'completed': return 'default';
            case 'processing': return 'secondary';
            case 'failed': return 'destructive';
            case 'cancelled': return 'outline';
            default: return 'secondary';
        }
    };
    const getStatusIcon = (status) => {
        switch (status) {
            case 'pending': return <Clock className="w-4 h-4" />;
            case 'completed': return <CheckCircle className="w-4 h-4" />;
            case 'processing': return <Clock className="w-4 h-4" />;
            case 'failed': return <XCircle className="w-4 h-4" />;
            case 'cancelled': return <AlertCircle className="w-4 h-4" />;
            default: return <Clock className="w-4 h-4" />;
        }
    };
    const stats = {
        total: transactions.length,
        matched: transactions.filter(t => t.matched_order_id).length,
        notifications: transactions.filter(t => t.notification_sent).length,
        totalCoins: transactions.reduce((sum, t) => sum + parseFloat(t.coin_amount || 0), 0),
        totalVND: transactions.filter(t => t.vnd_amount).reduce((sum, t) => sum + parseFloat(t.vnd_amount || 0), 0)
    };

    // Format currency
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount);
    };

    // Format date
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString('vi-VN');
    };

    // Get transaction source badge
    const getSourceBadge = (transaction) => {
        if (transaction.matched_order_id) {
            return <Badge variant="default">BuyCoin Order</Badge>;
        }
        return <Badge variant="secondary">Coin Monitor</Badge>;
    };

    useEffect(() => {
        fetchTransactions();

        // Auto refresh every 30 seconds with current pagination settings
        const interval = setInterval(() => {
            fetchTransactions(currentPage, itemsPerPage);
        }, 30000);
        return () => clearInterval(interval);
    }, [currentPage, itemsPerPage]);

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div>
                    <h2 className="text-2xl font-bold">Transaction History</h2>
                    <p className="text-muted-foreground">
                        Lịch sử giao dịch từ tất cả các worker
                    </p>
                </div>

                <div className="flex gap-2">
                    <Button
                        variant="outline"
                        onClick={() => fetchTransactions()}
                        disabled={loading}
                        className="flex items-center gap-2"
                    >
                        <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>

                    <Button
                        variant="destructive"
                        onClick={cleanupOldTransactions}
                        disabled={loading}
                        className="flex items-center gap-2"
                    >
                        <Trash2 className="h-4 w-4" />
                        Cleanup Old
                    </Button>
                </div>
            </div>

            {/* Error Alert */}
            {error && (
                <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                </Alert>
            )}

            {/* Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <Activity className="w-4 h-4 text-blue-600" />
                            <div className="text-sm font-medium">Total</div>
                        </div>
                        <div className="text-2xl font-bold text-blue-600 mt-2">
                            {stats.total}
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-green-600" />
                            <div className="text-sm font-medium">Matched</div>
                        </div>
                        <div className="text-2xl font-bold text-green-600 mt-2">
                            {stats.matched}
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <Bell className="w-4 h-4 text-purple-600" />
                            <div className="text-sm font-medium">Notified</div>
                        </div>
                        <div className="text-2xl font-bold text-purple-600 mt-2">
                            {stats.notifications}
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <Coins className="w-4 h-4 text-orange-600" />
                            <div className="text-sm font-medium">Total Coins</div>
                        </div>
                        <div className="text-2xl font-bold text-orange-600 mt-2">
                            {stats.totalCoins.toFixed(2)}
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent className="p-4">
                        <div className="flex items-center gap-2">
                            <Coins className="w-4 h-4 text-green-600" />
                            <div className="text-sm font-medium">Total VND</div>
                        </div>
                        <div className="text-lg font-bold text-green-600 mt-2">
                            {formatCurrency(stats.totalVND)}
                        </div>
                    </CardContent>
                </Card>
            </div>

            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Filter className="w-5 h-5" />
                        Filters
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label className="text-sm font-medium">Search</label>
                            <div className="relative">
                                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    placeholder="Search transactions..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>

                        <div>
                            <label className="text-sm font-medium">Status</label>
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Status</SelectItem>
                                    <SelectItem value="matched">Matched Orders</SelectItem>
                                    <SelectItem value="unmatched">Unmatched</SelectItem>
                                    <SelectItem value="notified">Notified</SelectItem>
                                    <SelectItem value="not_notified">Not Notified</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <label className="text-sm font-medium">Source</label>
                            <Select value={sourceFilter} onValueChange={setSourceFilter}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Sources</SelectItem>
                                    <SelectItem value="coin_monitor">Coin Monitor</SelectItem>
                                    <SelectItem value="buycoin_worker">BuyCoin Worker</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Transaction Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Transactions ({pagination.total})</CardTitle>
                    <CardDescription>
                        {lastUpdate && `Last updated: ${lastUpdate.toLocaleTimeString()}`}
                    </CardDescription>
                </CardHeader>

                <CardContent>
                    {loading && transactions.length === 0 ? (
                        <div className="text-center py-8">
                            <RefreshCw className="h-8 w-8 mx-auto mb-2 text-gray-400 animate-spin" />
                            <p className="text-gray-500">Loading transactions...</p>
                        </div>
                    ) : transactions.length === 0 ? (
                        <div className="text-center py-8">
                            <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                            <p className="text-gray-500">No transactions found</p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <div className="overflow-x-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Transaction ID</TableHead>
                                            <TableHead>Steam ID</TableHead>
                                            <TableHead>Coin Amount</TableHead>
                                            <TableHead>Order Match</TableHead>
                                            <TableHead>VND</TableHead>
                                            <TableHead>VND Uớc Tính</TableHead>
                                            <TableHead>Notification</TableHead>
                                            <TableHead>Trạng thái</TableHead>
                                            <TableHead>Date</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {transactions.map((transaction, index) => (
                                            <TableRow key={transaction.id || index}>
                                                <TableCell className="font-mono text-sm">
                                                    {transaction.transaction_id}
                                                </TableCell>
                                                <TableCell className="font-mono">
                                                    {transaction.steam_id}
                                                </TableCell>
                                                <TableCell>
                                                    <div className="flex items-center gap-1">
                                                        <Coins className="w-4 h-4 text-orange-500" />
                                                        {parseFloat(transaction.coin_amount || 0).toFixed(2)}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    {transaction.matched_order_id ? (
                                                        <Badge variant="default">
                                                            Order #{transaction.matched_order_id}
                                                        </Badge>
                                                    ) : (
                                                        <Badge variant="secondary">No match</Badge>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    {transaction.vnd_amount ?
                                                        formatCurrency(transaction.vnd_amount) :
                                                        '-'
                                                    }

                                                </TableCell>
                                                <TableCell>
                                                    {transaction.estimated_vnd ?
                                                        formatCurrency(transaction.estimated_vnd) :
                                                        '-'
                                                    }

                                                </TableCell>
                                                
                                                <TableCell>
                                                    {transaction.notification_sent ? (
                                                        <Badge variant="default" className="flex items-center gap-1 w-fit">
                                                            <CheckCircle className="w-3 h-3" />
                                                            Sent
                                                        </Badge>
                                                    ) : (
                                                        <Badge variant="secondary" className="flex items-center gap-1 w-fit">
                                                            <XCircle className="w-3 h-3" />
                                                            Not sent
                                                        </Badge>
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant={getStatusBadgeVariant(transaction.status)} className="flex items-center gap-1 w-fit">
                                                        {getStatusIcon(transaction.status)}
                                                        {transaction.status}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>
                                                    {formatDate(transaction.created_at)}
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>

                                {/* Pagination */}
                                <Pagination
                                    currentPage={pagination.page || 1}
                                    totalPages={pagination.totalPages || 1}
                                    totalItems={pagination.total || 0}
                                    itemsPerPage={itemsPerPage}
                                    onPageChange={handlePageChange}
                                    onItemsPerPageChange={handleItemsPerPageChange}
                                    showAlways={true}
                                    className="mt-4"
                                />
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
