"use client";
import { useState, useEffect, useCallback, useRef } from 'react';
import { checkBuyCoinOrderStatus } from '@/actions/buycoin';
import { LoadingSpinner } from "@/components/ui/enhanced-loading";
import { CheckCircle2, Clock, XCircle, AlertCircle, TrendingDown } from 'lucide-react';

export default function BuyCoinOrderStatus({ orderId, onOrderDataUpdate }) {
    const [orderData, setOrderData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const intervalRef = useRef(null);
    const lastCheckRef = useRef(0);

    const check = useCallback(async (force = false) => {
        if (!orderId) {
            setError('Không có mã đơn hàng');
            setLoading(false);
            return;
        }

        // Debounce: prevent too frequent calls (minimum 2 seconds between calls)
        const now = Date.now();
        if (!force && now - lastCheckRef.current < 2000) {
            return;
        }
        lastCheckRef.current = now;

        // Skip if order is already in final state (unless forced)
        if (!force && orderData) {
            const finalStatuses = ['completed', 'failed', 'cancelled'];
            if (finalStatuses.includes(orderData.status)) {
            
                return;
            }
        }

        try {
            setLoading(true);
            const data = await checkBuyCoinOrderStatus(orderId);
          

            if (!data || !data.success) {
                setError(data?.error || 'Không thể kiểm tra trạng thái đơn hàng');
            } else {
                setOrderData(data.data);
                setError(null);

                // Notify parent component about data update
                if (onOrderDataUpdate && data.data) {
                    onOrderDataUpdate(data.data);
                }

                // Stop interval if order is in final state
                const finalStatuses = ['completed', 'failed', 'cancelled'];
                if (finalStatuses.includes(data.data?.status) && intervalRef.current) {
                    clearInterval(intervalRef.current);
                    intervalRef.current = null;
                  
                }
            }
        } catch (err) {
        
            setError('Lỗi kết nối khi kiểm tra trạng thái');
        } finally {
            setLoading(false);
        }
    }, [orderId, orderData]);

    useEffect(() => {
        if (!orderId) return;

        // Initial load (forced)
        check(true);

        // Set up interval only once
        if (!intervalRef.current) {
            intervalRef.current = setInterval(() => {
                check(); // Not forced, will respect debounce and final state
            }, 5000);
        }

        return () => {
            if (intervalRef.current) {
                clearInterval(intervalRef.current);
                intervalRef.current = null;
            }
        };
    }, [orderId]); // Remove check from dependencies to prevent recreation

    const getStatusInfo = () => {
        if (error) {
            return {
                icon: XCircle,
                message: error,
                color: 'text-red-500',
                bgColor: 'bg-red-50 dark:bg-red-900/20',
                borderColor: 'border-red-200 dark:border-red-800'
            };
        }

        if (!orderData) {
            return {
                icon: Clock,
                message: 'Đang tải thông tin đơn hàng...',
                color: 'text-gray-500',
                bgColor: 'bg-gray-50 dark:bg-gray-800/50',
                borderColor: 'border-gray-200 dark:border-gray-700'
            };
        }

        // Map database status to display status
        switch (orderData.status) {
            case 'created':
                return {
                    icon: Clock,
                    message: 'Đang chờ gửi coin',
                    color: 'text-blue-500',
                    bgColor: 'bg-blue-50 dark:bg-blue-900/20',
                    borderColor: 'border-blue-200 dark:border-blue-800'
                };
            
            case 'processing':
                return {
                    icon: TrendingDown,
                    message: 'Đã nhận coin, đang xử lý chuyển tiền...',
                    color: 'text-orange-500',
                    bgColor: 'bg-orange-50 dark:bg-orange-900/20',
                    borderColor: 'border-orange-200 dark:border-orange-800'
                };
            
            case 'completed':
                return {
                    icon: CheckCircle2,
                    message: 'Đơn hàng hoàn thành thành công',
                    color: 'text-green-500',
                    bgColor: 'bg-green-50 dark:bg-green-900/20',
                    borderColor: 'border-green-200 dark:border-green-800'
                };
            
            case 'failed':
                return {
                    icon: XCircle,
                    message: orderData.admin_note || 'Đơn hàng thất bại',
                    color: 'text-red-500',
                    bgColor: 'bg-red-50 dark:bg-red-900/20',
                    borderColor: 'border-red-200 dark:border-red-800'
                };
            
            case 'cancelled':
                return {
                    icon: XCircle,
                    message: 'Đơn hàng đã bị hủy',
                    color: 'text-gray-500',
                    bgColor: 'bg-gray-50 dark:bg-gray-900/20',
                    borderColor: 'border-gray-200 dark:border-gray-800'
                };
            
            default:
                return {
                    icon: AlertCircle,
                    message: 'Đang xử lý đơn hàng...',
                    color: 'text-yellow-500',
                    bgColor: 'bg-yellow-50 dark:bg-yellow-900/20',
                    borderColor: 'border-yellow-200 dark:border-yellow-800'
                };
        }
    };

    const statusInfo = getStatusInfo();
    const IconComponent = statusInfo.icon;

    return (
        <div className={`p-4 rounded-lg border ${statusInfo.bgColor} ${statusInfo.borderColor} transition-all duration-300`}>
            <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                    {loading && !error && orderData?.status !== "completed" ? (
                        <LoadingSpinner size="sm" className={statusInfo.color} />
                    ) : (
                        <IconComponent className={`h-5 w-5 ${statusInfo.color}`} />
                    )}
                </div>
                <div className="flex-1">
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Trạng thái đơn hàng
                    </h3>
                    <p className={`text-sm font-medium ${statusInfo.color}`}>
                        {statusInfo.message}
                    </p>
                    {orderId && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Mã đơn hàng: {orderId}
                        </p>
                    )}
                    {orderData && orderData.updated_at && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            Cập nhật lần cuối: {new Date(orderData.updated_at).toLocaleString('vi-VN')}
                        </p>
                    )}
                </div>
            </div>
        </div>
    );
}
