"use client";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Coins } from "lucide-react";

export default function CoinInput({
  coin,
  vnd,
  buyRate,
  onCoinChange,
  register,
  errors,
  state
}) {
  const handleCoinChange = (e) => {
    const value = e.target.value;
    // Validate decimal places (max 2)
    if (/^\d*\.?\d{0,2}$/.test(value)) {
      onCoinChange(value);
    }
  };

  return (
    <>
      {/* Coin Input */}
      <div className="space-y-2 md:space-y-3">
        <Label htmlFor="coin" className="flex items-center space-x-2 text-base font-medium">
          <Coins className="h-4 w-4 text-orange-600" />
          <span>Số Coin Muốn Bán</span>
        </Label>
        <div className="relative">
          <Input
            type="text"
            {...register("coin")}
            value={coin}
            onChange={handleCoinChange}
            placeholder="Tối thiểu 5.00 coin"
            className="h-12 pl-4 text-lg font-semibold border-2 border-orange-200 dark:border-orange-600 focus:border-orange-500 transition-all duration-200"
            required
          />
          <Badge className="absolute right-3 top-3 text-xs bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
            COIN
          </Badge>
        </div>
        {errors.coin && (
          <p className="text-sm text-red-500 flex items-center space-x-1">
            <span>⚠️</span>
            <span>{errors.coin.message}</span>
          </p>
        )}
        {state?.errors?.coin && (
          <p className="text-sm text-red-500 flex items-center space-x-1">
            <span>⚠️</span>
            <span>{state.errors.coin[0]}</span>
          </p>
        )}
      </div>

      {/* VND Display */}
      {vnd && (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-4 rounded-xl border border-green-200 dark:border-green-800">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Số tiền bạn nhận được:</span>
            <span className="text-xl font-bold text-green-600 dark:text-green-400">
              {parseInt(vnd).toLocaleString()} VND
            </span>
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            💡 Tỷ giá thu mua: {(buyRate).toFixed(2)}
          </p>
        </div>
      )}
    </>
  );
}


