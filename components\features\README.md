# Cấu Tr<PERSON><PERSON> Features

## Tổng Quan
<PERSON> m<PERSON> `features` đã được tái cấu trúc để tách biệt các chức năng thành các module riê<PERSON> bi<PERSON>, d<PERSON> quản lý và bảo trì. Các component đã được tổ chức theo chức năng cụ thể.

## Cấu Trúc Mới

### 1. `/common` - Các Component Chung
- **OrderTabs.js**: Component chính quản lý các tab (di chuyển từ sellcoin/)
- Chức năng: Component dùng chung cho việc quản lý tabs

### 2. `/sellcoin` - Chức năng Mua Coin (Bán coin cho khách hàng)
- **SellCoinTab.js**: Component wrapper cho tab mua coin
- **SellCoinForm.js**: Form đặt hàng coin (đư<PERSON><PERSON> copy từ OrderForm.js)
- **SteamInfo.js**: Component hiển thị thông tin Steam
- Chức năng: <PERSON><PERSON><PERSON><PERSON> hàng mua coin từ hệ thống

### 3. `/orderinfo` - Các Component Liên Quan Đến Thông Tin Đơn Hàng
- **OrderInfo.js**: Component hiển thị thông tin thanh toán
- **BankInfo.js**: Component hiển thị thông tin ngân hàng
- **OrderStatus.js**: Component kiểm tra trạng thái đơn hàng
- Chức năng: Xử lý và hiển thị thông tin đơn hàng, thanh toán

### 4. `/buycoin` - Chức năng Thu Mua Coin (Mua coin từ khách hàng)
- **BuyCoinTab.js**: Component cho tab thu mua coin
- Chức năng: Hệ thống thu mua coin từ khách hàng
- Hiện tại: Đang trong giai đoạn phát triển, chỉ hiển thị thông tin liên hệ

### 5. `/support` - Chức năng Hỗ Trợ Khách Hàng
- **SupportTab.js**: Component cho tab hỗ trợ
- Chức năng: Hiển thị thông tin liên hệ và hỗ trợ khách hàng

## Thay Đổi Chính

### Tái cấu trúc hoàn toàn
- Thư mục `order/` đã được xóa hoàn toàn
- Các component được phân chia theo chức năng cụ thể
- Tất cả các import đã được cập nhật để trỏ đến vị trí mới

### OrderTabs.js (bây giờ trong common/)
- Di chuyển vào thư mục `common/` để dễ dàng tái sử dụng
- Import các component từ 4 thư mục riêng biệt: sellcoin, buycoin, support
- Quản lý tabs một cách tập trung

### Tách biệt OrderInfo components
- `OrderInfo.js`, `BankInfo.js`, `OrderStatus.js` được tách vào thư mục `orderinfo/`
- Dễ dàng tái sử dụng cho các chức năng khác nhau
- Tập trung xử lý logic liên quan đến thông tin đơn hàng

### Lợi Ích
1. **Tách biệt rõ ràng**: Mỗi chức năng có thư mục riêng
2. **Dễ bảo trì**: Code được tổ chức theo module cụ thể
3. **Tái sử dụng**: Các component có thể được sử dụng ở nhiều nơi
4. **Mở rộng**: Dễ dàng thêm tính năng mới cho từng module
5. **Tổ chức tốt hơn**: Component chung được đặt trong thư mục `common/`

## Sử Dụng

```jsx
// Import OrderTabs từ thư mục common
import OrderTabs from "@/components/features/common/OrderTabs";

// Import OrderInfo từ thư mục orderinfo
import OrderInfo from "@/components/features/orderinfo/OrderInfo";

// Sử dụng
<OrderTabs
  banks={banks}
  rate={rate}
  availableCoins={availableCoins}
  steamId={steamId}
  facebook={facebook}
/>
```

## Ghi Chú
- Tab "Mua Coin" sử dụng SellCoinTab (bán coin cho khách hàng)
- Tab "Thu Mua Coin" sử dụng BuyCoinTab (mua coin từ khách hàng)
- Tab "Hỗ Trợ" sử dụng SupportTab (hỗ trợ khách hàng)
- Các component liên quan đến thông tin đơn hàng được tập trung trong `orderinfo/`
- Component chung `OrderTabs` được đặt trong `common/` để dễ tái sử dụng
