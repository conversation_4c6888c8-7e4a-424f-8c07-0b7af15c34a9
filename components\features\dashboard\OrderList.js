"use client"
import { useState, useEffect } from 'react';
import { fetchUserOrder } from '@/lib/http';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { formatStatus } from '@/lib/utils';

export default function OrderList() {
    const [orders, setOrders] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);

    useEffect(() => {
        async function fetchOrders() {
            try {
                let { content, totalPages } = await fetchUserOrder(currentPage);
                setOrders(content);
                if (totalPages == 0) totalPages = 1
                setTotalPages(totalPages);
            } catch (error) {
                console.error(error);
            }
        }

        fetchOrders();
    }, [currentPage]);

    const handleNextPage = () => {

        if (currentPage < totalPages) {
            setCurrentPage((prev) => prev + 1);
        }
    };

    const handlePreviousPage = () => {
        if (currentPage > 1) {
            setCurrentPage((prev) => prev - 1);
        }
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Lịch sử mua hàng</CardTitle>
                <CardDescription>Xem các đơn đặt hàng trước đây của bạn</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="overflow-x-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Mã đơn hàng</TableHead>
                                <TableHead>SteamId</TableHead>
                                <TableHead>Số tiền</TableHead>
                                <TableHead>Trạng thái</TableHead>
                                <TableHead>Ngày đặt hàng</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {orders.map((order) => (
                                <TableRow key={order.orderId}>
                                    <TableCell>{order.orderId}</TableCell>
                                    <TableCell>{order.steamId}</TableCell>
                                    <TableCell>{order.amount.toLocaleString('de-DE', {
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 2,
                                    })}</TableCell>
                                    <TableCell>{formatStatus(order.status)}</TableCell>
                                    <TableCell>{
                                        `${new Date(order.createdAt).toLocaleDateString()} | ${new Date(order.createdAt).toLocaleTimeString()}`}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
                <div className="flex justify-between mt-4">
                    <Button onClick={handlePreviousPage} disabled={currentPage === 1}>
                        Lùi
                    </Button>
                    <span>Trang {currentPage} / {totalPages}</span>
                    <Button onClick={handleNextPage} disabled={currentPage === totalPages}>
                        Tới
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}