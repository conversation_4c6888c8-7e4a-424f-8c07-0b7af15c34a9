-- Migration: Create pending_transactions table
-- Description: Table to track transactions that have been detected but don't have matching orders yet

CREATE TABLE IF NOT EXISTS `pending_transactions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `transaction_id` BIGINT NOT NULL UNIQUE,
    `steam_id` VARCHAR(20) DEFAULT NULL,
    `coin_amount` DECIMAL(10, 2) NOT NULL,
    `delta_amount` INT NOT NULL,
    `transaction_type` VARCHAR(50) NOT NULL,
    `transaction_key` VARCHAR(50) NOT NULL,
    `transaction_timestamp` BIGINT NOT NULL,
    `transaction_date` DATETIME NOT NULL,
    `first_detected_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `last_checked_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `check_count` INT DEFAULT 1,
    `notification_sent` BO<PERSON><PERSON>N DEFAULT FALSE,
    `notification_sent_at` TIMESTA<PERSON> NULL DEFAULT NULL,
    `order_found` BOOLEAN DEFAULT FALSE,
    `order_found_at` TIMESTAMP NULL DEFAULT NULL,
    `matched_order_id` VARCHAR(50) DEFAULT NULL,
    `expired` BOOLEAN DEFAULT FALSE,
    `expired_at` TIMESTAMP NULL DEFAULT NULL,
    `status` ENUM('pending', 'matched', 'expired', 'cancelled') DEFAULT 'pending',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Indexes for performance
    INDEX `idx_transaction_id` (`transaction_id`),
    INDEX `idx_steam_id` (`steam_id`),
    INDEX `idx_transaction_timestamp` (`transaction_timestamp`),
    INDEX `idx_first_detected_at` (`first_detected_at`),
    INDEX `idx_status` (`status`),
    INDEX `idx_expired` (`expired`),
    INDEX `idx_order_found` (`order_found`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Comments for table structure:
-- id: Primary key, auto increment
-- transaction_id: CSGOEmpire transaction ID (unique)
-- steam_id: Steam ID from transaction data (if available)
-- coin_amount: Actual coin amount (delta / 100)
-- delta_amount: Raw delta amount from API
-- transaction_type: Type of transaction (Tip, Deposit, etc.)
-- transaction_key: Transaction key from API
-- transaction_timestamp: Original timestamp from API
-- transaction_date: Human readable date from API
-- first_detected_at: When this transaction was first detected
-- last_checked_at: Last time we checked for matching order
-- check_count: Number of times we've checked for matching order
-- notification_sent: Whether initial notification was sent
-- notification_sent_at: When initial notification was sent
-- order_found: Whether matching order was found
-- order_found_at: When matching order was found
-- matched_order_id: Order ID from buycoin table if matched
-- expired: Whether this transaction has expired (5+ minutes)
-- expired_at: When this transaction was marked as expired
-- status: Current status of the transaction tracking
-- created_at: Record creation time
-- updated_at: Last update time
