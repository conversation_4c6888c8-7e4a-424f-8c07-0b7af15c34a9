"use client";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LoadingSpinner } from "@/components/ui/enhanced-loading";
import { CreditCard, User } from "lucide-react";

export default function AccountInfo({
  accountNumber,
  accountName,
  selectedBank,
  loadingAccountName,
  onAccountNumberChange,
  register,
  errors
}) {
  const handleAccountNumberChange = (e) => {
    const value = e.target.value.replace(/\D/g, ''); // Only numbers
    onAccountNumberChange(value);
  };

  return (
    <>
      {/* Account Number */}
      <div className="space-y-2 md:space-y-3">
        <Label htmlFor="accountNumber" className="flex items-center space-x-2 text-base font-medium">
          <CreditCard className="h-4 w-4 text-orange-600" />
          <span>Số Tài Khoản</span>
        </Label>
        <div className="relative">
          <Input
            id="accountNumber"
            type="text"
            {...register("accountNumber")}
            value={accountNumber}
            onChange={handleAccountNumberChange}
            placeholder="Nhập số tài khoản ngân hàng"
            className="h-12 pl-4 text-lg border-2 border-gray-200 dark:border-gray-600 focus:border-orange-500 transition-all duration-200"
          />
        </div>
        {errors.accountNumber && (
          <p className="text-sm text-red-500 flex items-center space-x-1">
            <span>⚠️</span>
            <span>{errors.accountNumber.message}</span>
          </p>
        )}
      </div>

      {/* Account Name Display */}
      {selectedBank && accountNumber && (
        <div className="space-y-2 md:space-y-3">
          <Label className="flex items-center space-x-2 text-base font-medium">
            <User className="h-4 w-4 text-orange-600" />
            <span>Tên Chủ Tài Khoản</span>
          </Label>
          <div className="h-12 px-4 flex items-center bg-gray-50 dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-md">
            {loadingAccountName ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" />
                <span className="ml-2 text-sm text-gray-500">Đang tra cứu...</span>
              </div>
            ) : (
              <span className="text-lg font-medium text-gray-700 dark:text-gray-300">
                {accountName || "Có lỗi trong việc tra cứu tên chủ tài khoản"}
              </span>
            )}
          </div>
        </div>
      )}
    </>
  );
}


