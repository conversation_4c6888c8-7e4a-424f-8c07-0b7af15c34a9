"use client";
import { useState, useEffect } from "react";

const STORAGE_KEY = 'shared_steam_ids';

export function useSharedSteamIds() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [steamIds, setSteamIds] = useState([]);

  // Helper function to safely get from localStorage
  const getStoredValue = (key) => {
    if (typeof window === 'undefined') return null;
    try {
      const value = localStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.warn('Error reading from localStorage:', error);
      return null;
    }
  };

  // Helper function to safely set to localStorage
  const setStoredValue = (key, value) => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn('Error writing to localStorage:', error);
    }
  };

  // Migrate data from old storage keys
  const migrateOldData = () => {
    const buyCoinIds = getStoredValue('buycoin_steam_ids') || [];
    const sellCoinIds = getStoredValue('sellcoin_steam_ids') || [];

    // Combine and deduplicate
    const allIds = [...new Set([...buyCoinIds, ...sellCoinIds])];

    if (allIds.length > 0) {
      setStoredValue(STORAGE_KEY, allIds);

      // Clean up old storage
      if (typeof window !== 'undefined') {
        try {
          localStorage.removeItem('buycoin_steam_ids');
          localStorage.removeItem('sellcoin_steam_ids');
          console.log(`✅ Đã chuyển ${allIds.length} Steam ID sang bộ nhớ chung`);
        } catch (error) {
          console.warn('Error cleaning up old storage:', error);
        }
      }

      return allIds;
    }

    return [];
  };

  // Load stored data on mount
  useEffect(() => {
    let storedSteamIds = getStoredValue(STORAGE_KEY);

    // If no shared data exists, try to migrate from old storage
    if (!storedSteamIds || storedSteamIds.length === 0) {
      storedSteamIds = migrateOldData();
    }

    setSteamIds(storedSteamIds || []);
    setIsLoaded(true);
  }, []);

  // Add new Steam ID
  const addSteamId = (steamId) => {
    if (!steamId || steamIds.includes(steamId)) return;

    const updatedSteamIds = [...steamIds, steamId];
    setSteamIds(updatedSteamIds);
    setStoredValue(STORAGE_KEY, updatedSteamIds);
  };

  // Remove Steam ID
  const removeSteamId = (steamId) => {
    const updatedSteamIds = steamIds.filter(id => id !== steamId);
    setSteamIds(updatedSteamIds);
    setStoredValue(STORAGE_KEY, updatedSteamIds);
  };

  // Clear all data
  const clearAllData = () => {
    setSteamIds([]);
    setStoredValue(STORAGE_KEY, []);
  };

  return {
    isLoaded,
    steamIds,
    addSteamId,
    removeSteamId,
    clearAllData
  };
}
