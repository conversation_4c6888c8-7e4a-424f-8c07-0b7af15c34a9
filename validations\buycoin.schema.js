import { z } from "zod";

export const buyCoinSchema = z.object({
    steamId: z
        .string()
        .regex(/^\d{17}$/, { message: "Steam ID phải là chuỗi 17 chữ số" }),
    coin: z
        .string()
        .regex(/^\d+(\.\d{1,2})?$/, { message: "Coin phải là số thực với tối đa 2 chữ số thập phân" })
        .refine((val) => {
            const num = parseFloat(val);
            return num >= 5;
        }, { message: "Số coin tối thiểu là 5.00" }),
    bankCode: z
        .string()
        .nonempty({ message: "Vui lòng chọn ngân hàng" }),
    bin: z
        .string()
        .regex(/^\d{6}$/, { message: "BIN phải là chuỗi 6 chữ số" })
        .optional(),
    accountNumber: z
        .string()
        .regex(/^\d{6,20}$/, { message: "Số tài khoản phải từ 6-20 chữ số" }),
});
