"use strict";
import { Avatar, AvatarImage } from "@/components/ui/avatar"
export default function SteamInfo({
    steamId, steamName, coin, steamLevel, money, steamAvatar
}) {
    const info = [
        { label: "Steam ID", value: steamId },
        { label: "Steam Name:", value: steamName },
        {
            label: "Steam avatar", value: <Avatar className="cursor-pointer">
                <AvatarImage src={steamAvatar} alt={steamName} />
            </Avatar>
        },

    ]
    return (
       <></>
    )
}
