-- Migration: Update transaction_tracking table to handle both processed and pending transactions
-- Description: Add columns to support pending transaction tracking and status management

-- Add new columns for pending transaction support
ALTER TABLE `transaction_tracking` 
ADD COLUMN `status` ENUM('pending', 'matched', 'expired', 'cancelled') DEFAULT 'pending' AFTER `matched_order_id`,
ADD COLUMN `first_detected_at` TIMESTAMP NULL DEFAULT NULL AFTER `status`,
ADD COLUMN `last_checked_at` TIMESTAMP NULL DEFAULT NULL AFTER `first_detected_at`,
ADD COLUMN `check_count` INT DEFAULT 1 AFTER `last_checked_at`,
ADD COLUMN `order_found` BOOLEAN DEFAULT FALSE AFTER `check_count`,
ADD COLUMN `order_found_at` TIMESTAMP NULL DEFAULT NULL AFTER `order_found`,
ADD COLUMN `expired` BOOLEAN DEFAULT FALSE AFTER `order_found_at`,
ADD COLUMN `expired_at` TIMESTAMP NULL DEFAULT NULL AFTER `expired`;

-- Add indexes for new columns
ALTER TABLE `transaction_tracking`
ADD INDEX `idx_status` (`status`),
ADD INDEX `idx_first_detected_at` (`first_detected_at`),
ADD INDEX `idx_order_found` (`order_found`),
ADD INDEX `idx_expired` (`expired`);



-- Comments for new columns:
-- status: Current status of transaction tracking (pending, processed, matched, expired, cancelled)
-- first_detected_at: When this transaction was first detected (for pending transactions)
-- last_checked_at: Last time we checked for matching order (for pending transactions)
-- check_count: Number of times we've checked for matching order
-- order_found: Whether matching order was found
-- order_found_at: When matching order was found
-- expired: Whether this transaction has expired (5+ minutes for pending)
-- expired_at: When this transaction was marked as expired
