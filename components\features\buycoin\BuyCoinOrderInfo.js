"use client";
import { useState, useEffect, useRef, useCallback } from "react";
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Clock, AlertCircle, CheckCircle2, TrendingDown } from "lucide-react";
import BuyCoinOrderStatus from "./BuyCoinOrderStatus";
import BuyCoinInfo from "./BuyCoinInfo";

export default function BuyCoinOrderInfo({ data }) {
  // Thiết lập thời gian 5 phút (300 giây)
  const [timeLeft, setTimeLeft] = useState(300);
  const [currentOrderData, setCurrentOrderData] = useState(null);
  const intervalRef = useRef(null);

  // Callback để nhận data từ BuyCoinOrderStatus
  const handleOrderDataUpdate = useCallback((orderData) => {
    setCurrentOrderData(orderData);
  }, []);

  useEffect(() => {
    if (!data) return;

    intervalRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 0) {
          clearInterval(intervalRef.current);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [data]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const getTimeStatus = () => {
    if (timeLeft > 240) return { color: "bg-green-500", text: "Còn nhiều thời gian" };
    if (timeLeft > 120) return { color: "bg-yellow-500", text: "Sắp hết thời gian" };
    if (timeLeft > 0) return { color: "bg-orange-500", text: "Gấp rút gửi coin" };
    return { color: "bg-red-500", text: "Hết thời gian" };
  };

  const timeStatus = getTimeStatus();

  // Kiểm tra nếu không có data
  if (!data) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 text-lg">Không thể tải thông tin đơn hàng</p>
          <p className="text-gray-500 mt-2">Vui lòng thử lại sau</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 p-4">
      <div className="max-w-2xl mx-auto">
        {/* Header Section */}
        <div className="text-center mb-3">
          <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Thông Tin Bán Coin
          </h1>
        </div>

        {/* Timer Section */}
        <Card className="mb-3 border-0 shadow-md bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm">
          <CardContent className="p-2">
            <div className="flex items-center justify-center space-x-3">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Thời gian gửi coin:
              </span>
              <div className={`w-2 h-2 rounded-full ${timeStatus.color} animate-pulse`}></div>
              <span className="text-lg font-bold text-gray-900 dark:text-white font-mono">
                {formatTime(timeLeft)}
              </span>
              <Badge variant={timeLeft > 120 ? "default" : "destructive"} className="text-xs px-2 py-0.5">
                {timeStatus.text}
              </Badge>
            </div>
            {timeLeft <= 0 && (
              <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-800">
                <div className="flex items-center space-x-2 text-red-700 dark:text-red-400">
                  <AlertCircle className="h-3 w-3 flex-shrink-0" />
                  <span className="font-semibold text-xs">⏰ Hết thời gian! Kiểm tra trạng thái bên dưới</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Order Details Section */}
        <Card className="mb-3 border-0 shadow-lg bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm">
          <CardHeader className="p-3 text-center">
            <CardTitle className="flex items-center justify-center space-x-2 text-base text-gray-800 dark:text-gray-100">
              <TrendingDown className="h-4 w-4 text-blue-600" />
              <span>Chi Tiết Đơn Bán Coin</span>
            </CardTitle>
          </CardHeader>

          <CardContent className="p-3 pt-0">
            <BuyCoinInfo data={data} currentStatus={currentOrderData?.status} />

            {/* Target Steam ID */}
            <div className="mt-3 p-3 bg-gradient-to-r from-blue-100/60 to-indigo-100/60 dark:from-blue-800/20 dark:to-indigo-800/20 rounded-lg backdrop-blur-sm border border-blue-200/50 dark:border-blue-700/30">
              <div className="text-center">
                <p className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-1">
                  🎯 Gửi coin đến Steam ID:
                </p>
                <p className="text-lg font-bold text-blue-900 dark:text-blue-100 font-mono bg-white/50 dark:bg-slate-800/50 px-3 py-1 rounded border">
                  76561199172430695
                </p>
              </div>
            </div>

            <div className="mt-3 p-2 bg-gradient-to-r from-green-100/60 to-emerald-100/60 dark:from-green-800/20 dark:to-emerald-800/20 rounded-lg backdrop-blur-sm border border-green-200/50 dark:border-green-700/30">
              <div className="flex items-start space-x-2">
                <CheckCircle2 className="h-3 w-3 mt-0.5 text-green-600 dark:text-green-400 flex-shrink-0" />
                <p className="text-xs leading-relaxed text-gray-700 dark:text-gray-300">
                  <span className="font-semibold">Lưu ý:</span> Vui lòng gửi coin trong vòng 5 phút trước hoặc sau khi tạo đơn hàng..
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Order Status Section */}
        <div className="mt-3">
          <Card className="border-0 shadow-md bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm">
            <CardHeader className="p-2">
              <CardTitle className="text-sm text-gray-800 dark:text-gray-100">
                Trạng Thái Đơn Hàng
              </CardTitle>
            </CardHeader>

            <CardContent className="p-2 pt-0">
              <BuyCoinOrderStatus orderId={data.orderId} onOrderDataUpdate={handleOrderDataUpdate} />
            </CardContent>
          </Card>
        </div>

        {/* Features Section - Minimal */}
        <div className="mt-3 mb-4 p-2 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/10 dark:to-pink-900/10 rounded-lg border border-purple-100 dark:border-purple-800/30">
          <div className="flex justify-center space-x-6 text-center">
            <div className="flex items-center space-x-1">
              <CheckCircle2 className="h-3 w-3 text-green-600 dark:text-green-400" />
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Tự Động</span>
            </div>

            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3 text-blue-600 dark:text-blue-400" />
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">5 phút</span>
            </div>

            <div className="flex items-center space-x-1">
              <TrendingDown className="h-3 w-3 text-purple-600 dark:text-purple-400" />
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">An Toàn</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
