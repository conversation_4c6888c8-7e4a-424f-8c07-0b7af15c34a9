import { NextResponse } from 'next/server'
import { settingCookies } from '@/lib/utils'
import { steamSignIn } from '@/lib/steamSignIn';
import { sendLoginSteam } from '@/lib/http';
export async function GET(request) {
    let steamId = await steamSignIn.verifyLogin(request.url)
    const steamid64 = steamId.getSteamID64();
    const data = await sendLoginSteam(`steam_id=${steamid64}&keytoken=23scsdsas`)
    const res = NextResponse.redirect(new URL(process.env.REALM))
    res.cookies.set('token', data.accessToken, settingCookies)
    return res
}