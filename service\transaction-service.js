'use server';
import { fetchHome } from '@/lib/http.js';
import { execute, query } from '../lib/db.js';
import { updateBuyCoinOrder } from './buy-coin-service.js';

/**
 * Transaction Tracking Service
 * Manages tracking of processed CSGOEmpire transactions
 */

/**
 * Check if transaction has already been processed
 * @param {number} transactionId - CSGOEmpire transaction ID
 * @returns {Promise<Object>} Check result
 */
export async function isTransactionProcessed(transactionId) {
    try {
        const queryStr = `
            SELECT id, transaction_id, notification_sent, processed_at
            FROM transaction_tracking
            WHERE transaction_id = ?
            LIMIT 1
        `;

        // Sử dụng destructuring để lấy trực tiếp rows
        const [rows] = await query(queryStr, [transactionId]);
        return {
            success: true,
            isProcessed: rows.length > 0,
            data: rows.length > 0 ? rows[0] : null
        };

    } catch (error) {
        console.error('Error checking transaction processed status:', error);
        return {
            success: false,
            error: error.message,
            isProcessed: false
        };
    }
}

/**
 * Save processed transaction to tracking table
 * @param {Object} transactionData - Transaction data from CSGOEmpire
 * @param {string} matchedOrderId - Matched buycoin order ID (if any)
 * @returns {Promise<Object>} Save result
 */
export async function saveTransactionAndUpdateBuyCoinOrder(transactionData, matchedOrderId) {
    try {
        // If we have a matched order, update its status to 'processing'

        const updateResult = await updateBuyCoinOrder(matchedOrderId, {
            status: 'processing',
            adminNote: `Auto-processed: Transaction ${transactionData.id} matched`,
            processedBy: 'system_worker'
        });

        if (!updateResult.success) {
            console.error(`Failed to update order ${matchedOrderId} to processing:`, updateResult.error);
            // Continue with transaction tracking even if order update fails
        } else {

        }

        const queryStr = `
    INSERT INTO transaction_tracking (
        transaction_id,
        steam_id,
        coin_amount,
        delta_amount,
        transaction_type,
        transaction_key,
        transaction_timestamp,
        transaction_date,
        matched_order_id,
        status,
        estimated_vnd,
        order_found,
        order_found_at,
        notification_sent,
        processed_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
`;

        const steamId = transactionData.data?.steam_id || null;
        const coinAmount = parseFloat(transactionData.delta_coins || 0);
        const deltaAmount = parseInt(transactionData.delta || 0);
        const transactionType = transactionData.type || 'Unknown';
        const transactionKey = transactionData.key || 'unknown';
        const transactionTimestamp = transactionData.timestamp_raw || Date.now();
        const transactionDate = new Date(transactionData.date || Date.now());

        const status = 'matched';
        const orderFound = true
        const orderFoundAt = new Date();
        const home = await fetchHome()
        let estimatedVnd = 0;
        if (home) {
            const { coinBuyRate } = home
            estimatedVnd = Math.round(deltaAmount * coinBuyRate * 10);
        }

        const values = [
            transactionData.id, // transaction_id (first parameter now)
            steamId,
            coinAmount,
            deltaAmount,
            transactionType,
            transactionKey,
            transactionTimestamp,
            transactionDate.toISOString().slice(0, 19).replace('T', ' '),
            matchedOrderId,
            status,
            estimatedVnd,
            orderFound,
            orderFoundAt,
            false  // notification_sent
        ];

        const [result] = await execute(queryStr, values);

        // Get the inserted data back using the auto-generated ID
        const selectQuery = `
            SELECT * FROM transaction_tracking
            WHERE id = ?
            LIMIT 1
        `;
        const [insertedData] = await execute(selectQuery, [result.insertId]);

        return {
            success: true,
            insertId: result.insertId,
            transactionId: transactionData.id,
            orderUpdated: !!matchedOrderId,
            data: insertedData.length > 0 ? insertedData[0] : null
        };

    } catch (error) {
        console.error('Error saving processed transaction:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Update notification status for a transaction
 * @param {number} transactionId - CSGOEmpire transaction ID
 * @param {boolean} notificationSent - Whether notification was sent successfully
 * @returns {Promise<Object>} Update result
 */
export async function updateNotificationStatus(transactionId, notificationSent = true) {
    try {
        const equery = `
            UPDATE transaction_tracking 
            SET notification_sent = ?, 
                notification_sent_at = ${notificationSent ? 'NOW()' : 'NULL'},
                updated_at = NOW()
            WHERE transaction_id = ?
        `;

        const finalQuery = equery
            .replace('?', notificationSent ? 'TRUE' : 'FALSE')
            .replace('?', transactionId.toString());
        const [result] = await query(finalQuery);

        return {
            success: true,
            affectedRows: result.affectedRows
        };

    } catch (error) {
        console.error('Error updating notification status:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * Find matching buycoin order for a transaction
 * @param {string} steamId - Steam ID from transaction
 * @param {number} coinAmount - Coin amount from transaction
 * @param {number} transactionTimestamp - Transaction timestamp
 * @returns {Promise<Object>} Matching order result
 */
export async function findMatchingBuyCoinOrder(steamId, coinAmount, transactionTimestamp) {
    try {
        if (!steamId) {
            return {
                success: true,
                hasMatch: false,
                data: null
            };
        }

        // Convert timestamp to Date for comparison
        // const transactionDate = new Date(transactionTimestamp * 1000);
        //  const fiveMinutesAgo = new Date(Date.now() - (5 * 60 * 1000));
        const queryString = `
    SELECT * 
    FROM buycoin 
    WHERE steam_id = ? 
      AND coin_amount = ? 
      AND status = 'created'
      AND created_at >= NOW() - INTERVAL 5 MINUTE
    ORDER BY created_at DESC
    LIMIT 1
`;

        const [rows] = await query(queryString, [
            steamId,
            coinAmount,
        ]);

        return {
            success: true,
            hasMatch: rows.length > 0,
            data: rows.length > 0 ? rows[0] : null
        };

    } catch (error) {
        console.error('Error finding matching buycoin order:', error);
        return {
            success: false,
            error: error.message,
            hasMatch: false,
            data: null
        };
    }
}

/**
 * Get recent processed transactions with pagination
 * @param {number} limit - Number of records to fetch per page
 * @param {number} page - Page number (1-based)
 * @returns {Promise<Object>} Recent transactions with pagination
 */
export async function getRecentProcessedTransactions(limit = 50, page = 1) {
    try {
        // Ensure limit and page are valid integers
        const validLimit = Math.max(1, Math.min(parseInt(limit) || 50, 200));
        const validPage = Math.max(1, parseInt(page) || 1);
        const offset = (validPage - 1) * validLimit;

        // First check if table exists and has data
        const countQuery = 'SELECT COUNT(*) as count FROM transaction_tracking';
        const [countResult] = await execute(countQuery);
        const totalCount = countResult[0].count;

        if (totalCount === 0) {
            return {
                success: true,
                data: [],
                pagination: {
                    page: validPage,
                    limit: validLimit,
                    total: 0,
                    totalPages: 0
                }
            };
        }

        const equery = `
            SELECT
                tt.id,
                tt.transaction_id,
                tt.steam_id,
                tt.coin_amount,
                tt.delta_amount,
                tt.transaction_type,
                tt.transaction_key,
                tt.transaction_timestamp,
                tt.transaction_date,
                tt.matched_order_id,
                tt.notification_sent,
                tt.notification_sent_at,
                tt.estimated_vnd,
                tt.processed_at,
                tt.created_at,
                tt.updated_at,
                tt.status,
                bc.order_id as order_order_id,
                bc.vnd_amount,
                bc.bank_code,
                bc.account_number,
                bc.account_name,
                bc.status as order_status
            FROM transaction_tracking tt
            LEFT JOIN buycoin bc ON tt.matched_order_id = bc.order_id
            ORDER BY tt.processed_at DESC
            LIMIT ? OFFSET ?
        `;

        // Use query instead of execute to avoid parameter binding issues with LIMIT/OFFSET
        const finalQuery = equery.replace('LIMIT ? OFFSET ?', `LIMIT ${validLimit} OFFSET ${offset}`);
        const [rows] = await query(finalQuery);

        return {
            success: true,
            data: rows,
            pagination: {
                page: validPage,
                limit: validLimit,
                total: totalCount,
                totalPages: Math.ceil(totalCount / validLimit)
            }
        };

    } catch (error) {
        console.error('Error fetching recent processed transactions:', error);
        return {
            success: false,
            error: error.message,
            data: [],
            pagination: {
                page: 1,
                limit: 50,
                total: 0,
                totalPages: 0
            }
        };
    }
}

/**
 * Clean up old processed transactions (older than 30 days)
 * @returns {Promise<Object>} Cleanup result
 */
export async function cleanupOldTransactions() {
    try {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const equery = `
            DELETE FROM transaction_tracking 
            WHERE processed_at < ?
        `;

        const finalQuery = equery.replace('?', `'${thirtyDaysAgo.toISOString().slice(0, 19).replace('T', ' ')}'`);
        const [result] = await query(finalQuery);

        return {
            success: true,
            deletedRows: result.affectedRows
        };

    } catch (error) {
        console.error('Error cleaning up old transactions:', error);
        return {
            success: false,
            error: error.message
        };
    }
}
