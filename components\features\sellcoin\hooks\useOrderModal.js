"use client";
import { useState, useEffect, useCallback } from "react";

export function useOrderModal() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [lastOrderId, setLastOrderId] = useState(null);

  const handleModalClose = useCallback((formAction, reset, resetCurrency) => {
    setIsModalOpen(false);
    // Reset form state to allow new orders to show popup
    formAction(null);
    // Reset form fields for new order
    reset();
    resetCurrency();
  }, []);

  const handleOrderSuccess = useCallback((state) => {
    if (state?.success && state?.data?.orderId) {
      // Only open modal for new orders (different orderId)
      if (state.data.orderId !== lastOrderId) {
        setIsModalOpen(true);
        setLastOrderId(state.data.orderId);
      }
    }
  }, [lastOrderId]);

  return {
    isModalOpen,
    handleModalClose,
    handleOrderSuccess
  };
}
