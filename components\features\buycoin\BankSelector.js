"use client";
import { useState } from "react";
import { Label } from "@/components/ui/label";
import { LoadingSpinner } from "@/components/ui/enhanced-loading";
import { Building2, Check, ChevronsUpDown } from "lucide-react";
import { useBankData } from "./hooks/useBankData";

export default function BankSelector({
  selectedBank,
  onBankSelect,
  register,
  errors
}) {
  const [bankSearchOpen, setBankSearchOpen] = useState(false);
  const [bankSearchValue, setBankSearchValue] = useState("");

  // Use hook to get bank data
  const { banks, loadingBanks } = useBankData();

  // Filter banks based on search
  const filteredBanks = banks.filter(bank => {
    if (!bankSearchValue) return true;
    const searchLower = bankSearchValue.toLowerCase();
    return (
      bank.name.toLowerCase().includes(searchLower) ||
      bank.shortName.toLowerCase().includes(searchLower) ||
      bank.short_name?.toLowerCase().includes(searchLower) ||
      bank.code.toLowerCase().includes(searchLower)
    );
  });

  // Get selected bank info
  const selectedBankInfo = banks.find(bank => bank.code === selectedBank);

  const handleBankSelect = (bankCode) => {
    onBankSelect(bankCode);
    setBankSearchOpen(false);
    setBankSearchValue("");
  };

  const handleSearchChange = (e) => {
    setBankSearchValue(e.target.value);
  };

  const toggleBankSearch = () => {
    setBankSearchOpen(!bankSearchOpen);
  };

  return (
    <div className="space-y-2 md:space-y-3">
      <Label htmlFor="bankCode" className="flex items-center space-x-2 text-base font-medium">
        <Building2 className="h-4 w-4 text-orange-600" />
        <span>Ngân Hàng Nhận Tiền</span>
      </Label>

      {loadingBanks ? (
        <div className="h-12 flex items-center justify-center border-2 border-gray-200 rounded-md">
          <LoadingSpinner size="sm" />
          <span className="ml-2 text-sm text-gray-500">Đang tải ngân hàng...</span>
        </div>
      ) : banks.length === 0 ? (
        <div className="h-12 flex items-center justify-center border-2 border-red-200 rounded-md bg-red-50">
          <span className="text-sm text-red-600">Không thể tải danh sách ngân hàng</span>
        </div>
      ) : (
        <div className="relative">
          <button
            type="button"
            onClick={toggleBankSearch}
            className="h-12 w-full flex items-center justify-between text-lg border-2 border-gray-200 dark:border-gray-600 hover:border-orange-500 focus:border-orange-500 focus-visible:ring-2 focus-visible:ring-orange-500 focus-visible:ring-offset-2 transition-all duration-200 bg-white dark:bg-slate-800 rounded-md px-3 cursor-pointer relative z-10"
          >
            {selectedBankInfo ? (
              <div className="flex items-center space-x-4 flex-1 min-w-0">
                <img
                  src={selectedBankInfo.logo}
                  alt={selectedBankInfo.shortName || selectedBankInfo.short_name}
                  className="w-14 h-10 flex-shrink-0 object-contain rounded-md border border-gray-300 dark:border-gray-600 bg-white p-1"
                />
                <span className="truncate font-medium text-gray-900 dark:text-slate-100">
                  {selectedBankInfo.shortName || selectedBankInfo.short_name} - {selectedBankInfo.name}
                </span>
              </div>
            ) : (
              <span className="text-gray-500 dark:text-gray-400 font-medium">Chọn ngân hàng nhận tiền...</span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </button>

          {bankSearchOpen && (
            <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white dark:bg-slate-800 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-2xl">
              <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                <input
                  type="text"
                  placeholder="Tìm kiếm ngân hàng..."
                  value={bankSearchValue}
                  onChange={handleSearchChange}
                  className="w-full h-10 px-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-slate-800 text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500"
                  autoFocus
                />
              </div>
              <div className="max-h-[300px] overflow-y-auto">
                {filteredBanks.length === 0 ? (
                  <div className="py-8 text-center text-base font-medium text-gray-600 dark:text-gray-300">
                    Không tìm thấy ngân hàng.
                  </div>
                ) : (
                  filteredBanks.map((bank) => (
                    <div
                      key={bank.code}
                      onClick={() => handleBankSelect(bank.code)}
                      className="flex items-center space-x-4 p-2 cursor-pointer hover:bg-blue-300 dark:hover:bg-slate-600 transition-colors duration-150 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                    >
                      <img
                        src={bank.logo}
                        alt={bank.shortName || bank.short_name}
                        className="w-16 h-10 flex-shrink-0 object-contain rounded-md border border-gray-300 dark:border-gray-600 bg-slate-200 p-1.5 shadow-sm"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-base text-gray-700 dark:text-slate-100 truncate">
                          {bank.shortName || bank.short_name}
                        </div>
                        <div className="text-sm text-gray-700 dark:text-gray-300 truncate mt-1">
                          {bank.name}
                        </div>
                      </div>
                      {selectedBank === bank.code && (
                        <Check className="ml-auto h-5 w-5 text-orange-600 dark:text-orange-400" />
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          )}
        </div>
      )}
      
      <input type="hidden" {...register("bankCode")} value={selectedBank} />
      <input type="hidden" {...register("bin")} value={selectedBankInfo?.bin || ""} />
      {errors.bankCode && (
        <p className="text-sm text-red-500 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{errors.bankCode.message}</span>
        </p>
      )}
      {errors.bin && (
        <p className="text-sm text-red-500 flex items-center space-x-1">
          <span>⚠️</span>
          <span>{errors.bin.message}</span>
        </p>
      )}
    </div>
  );
}


