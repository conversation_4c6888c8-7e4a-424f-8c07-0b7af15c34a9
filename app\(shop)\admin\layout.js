'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { BarChart3, Activity, ShoppingCart, History, Settings, User, LogOut } from 'lucide-react';

// Import components
import AdminOverview from '@/components/features/admin/AdminOverview';
import WorkersManagement from '@/components/features/admin/WorkersManagement';
import BuyCoinOrdersManagement from '@/components/features/admin/BuyCoinOrdersManagement';
import TransactionHistoryAdmin from '@/components/features/admin/TransactionHistoryAdmin';

export default function AdminLayout({ children }) {
    const [activeTab, setActiveTab] = useState('overview');
    const [adminInfo, setAdminInfo] = useState(null);
    const [loading, setLoading] = useState(true);

    // Fetch admin info
    useEffect(() => {
        const fetchAdminInfo = async () => {
            try {
                const [userResponse] = await Promise.all([
                    fetch('/api/auth/me'),
                ]);

                const userData = await userResponse.json();

                if (userData.success) {
                    setAdminInfo({
                        ...userData.user,
                        adminType: adminData.success ? adminData.adminAuth?.adminType : null
                    });
                }
            } catch (error) {
                console.error('Error fetching admin info:', error);
            } finally {
                setLoading(false);
            }
        };
        fetchAdminInfo();
    }, []);

    // Logout function
    const handleLogout = async () => {
        try {
            await fetch('/api/auth/logout', { method: 'POST' });
            window.location.href = '/login';
        } catch (error) {
            console.error('Error logging out:', error);
        }
    };

    return (
        <div className="container mx-auto p-6 space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-3xl font-bold">Admin Dashboard</h1>
                    <p className="text-muted-foreground">
                        Quản lý hệ thống, workers và đơn hàng
                    </p>
                </div>

                <div className="flex items-center gap-4">
                    {adminInfo && (
                        <div className="flex items-center gap-2">
                            <User className="w-4 h-4" />
                            <div className="flex flex-col">
                                <span className="text-sm font-medium">{adminInfo.steamName || adminInfo.name || 'Admin'}</span>
                                {adminInfo.adminType && (
                                    <span className="text-xs text-muted-foreground">
                                        via {adminInfo.adminType === 'steam' ? 'Steam' : 'Google'}
                                    </span>
                                )}
                            </div>
                        </div>
                    )}
                    <Badge variant="outline" className="text-sm">
                        Admin Panel
                    </Badge>
                    <Button variant="outline" size="sm" onClick={handleLogout}>
                        <LogOut className="w-4 h-4 mr-2" />
                        Logout
                    </Button>
                </div>
            </div>

            {/* Tab Navigation */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                <TabsList className="grid w-full grid-cols-4">
                    <TabsTrigger value="overview" className="flex items-center gap-2">
                        <BarChart3 className="w-4 h-4" />
                        Tổng quan
                    </TabsTrigger>
                    <TabsTrigger value="workers" className="flex items-center gap-2">
                        <Activity className="w-4 h-4" />
                        Workers
                    </TabsTrigger>
                    <TabsTrigger value="buycoin-orders" className="flex items-center gap-2">
                        <ShoppingCart className="w-4 h-4" />
                        BuyCoin Orders
                    </TabsTrigger>
                    <TabsTrigger value="transactions" className="flex items-center gap-2">
                        <History className="w-4 h-4" />
                        Transactions
                    </TabsTrigger>
                </TabsList>

                <TabsContent value="overview">
                    <AdminOverview />
                </TabsContent>

                <TabsContent value="workers">
                    <WorkersManagement />
                </TabsContent>

                <TabsContent value="buycoin-orders">
                    <BuyCoinOrdersManagement />
                </TabsContent>

                <TabsContent value="transactions">
                    <TransactionHistoryAdmin />
                </TabsContent>
            </Tabs>
        </div>
    );
}
