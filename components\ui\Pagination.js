import React from 'react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

const Pagination = ({
    currentPage = 1,
    totalPages = 1,
    totalItems = 0,
    itemsPerPage = 10,
    onPageChange,
    onItemsPerPageChange,
    showItemsPerPage = true,
    showPageInfo = true,
    showFirstLast = true,
    showAlways = false,
    className = ""
}) => {
    // Ensure we have valid values
    const validCurrentPage = Math.max(1, currentPage || 1);
    const validTotalPages = Math.max(1, totalPages || 1);
    const validTotalItems = Math.max(0, totalItems || 0);
    const validItemsPerPage = Math.max(1, itemsPerPage || 10);

    const startItem = validTotalItems > 0 ? (validCurrentPage - 1) * validItemsPerPage + 1 : 0;
    const endItem = Math.min(validCurrentPage * validItemsPerPage, validTotalItems);

    const handlePageChange = (page) => {
        if (page >= 1 && page <= validTotalPages && page !== validCurrentPage && onPageChange) {
            onPageChange(page);
        }
    };

    const handleItemsPerPageChange = (value) => {
        const newItemsPerPage = parseInt(value);
        if (onItemsPerPageChange) {
            onItemsPerPageChange(newItemsPerPage);
        }
    };

    // Generate page numbers to show
    const getPageNumbers = () => {
        const pages = [];
        const maxVisiblePages = 5;

        if (validTotalPages <= maxVisiblePages) {
            // Show all pages if total is small
            for (let i = 1; i <= validTotalPages; i++) {
                pages.push(i);
            }
        } else {
            // Show pages around current page
            let start = Math.max(1, validCurrentPage - 2);
            let end = Math.min(validTotalPages, validCurrentPage + 2);

            // Adjust if we're near the beginning or end
            if (validCurrentPage <= 3) {
                end = Math.min(validTotalPages, 5);
            } else if (validCurrentPage >= validTotalPages - 2) {
                start = Math.max(1, validTotalPages - 4);
            }

            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
        }

        return pages;
    };

    const pageNumbers = getPageNumbers();

    if (validTotalPages <= 1 && !showAlways) {
        return null; // Don't show pagination if there's only one page
    }

    return (
        <div className={`flex items-center justify-between gap-4 ${className}`}>
            {/* Page Info */}
            {showPageInfo && (
                <div className="text-sm text-gray-600">
                    {validTotalItems > 0
                        ? `Showing ${startItem} to ${endItem} of ${validTotalItems} entries`
                        : 'No entries found'
                    }
                </div>
            )}

            {/* Pagination Controls */}
            <div className="flex items-center gap-2">
                {/* Items per page selector */}
                {showItemsPerPage && (
                    <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">Show</span>
                        <Select value={validItemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
                            <SelectTrigger className="w-20">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="25">25</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                            </SelectContent>
                        </Select>
                        <span className="text-sm text-gray-600">entries</span>
                    </div>
                )}

                {/* Page navigation */}
                <div className="flex items-center gap-1">
                    {/* First page */}
                    {showFirstLast && validCurrentPage > 1 && (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(1)}
                            disabled={validCurrentPage === 1}
                        >
                            <ChevronsLeft className="w-4 h-4" />
                        </Button>
                    )}

                    {/* Previous page */}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(validCurrentPage - 1)}
                        disabled={validCurrentPage === 1}
                    >
                        <ChevronLeft className="w-4 h-4" />
                    </Button>

                    {/* Page numbers */}
                    {pageNumbers.map((pageNum) => (
                        <Button
                            key={pageNum}
                            variant={pageNum === validCurrentPage ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(pageNum)}
                            className="min-w-[40px]"
                        >
                            {pageNum}
                        </Button>
                    ))}

                    {/* Next page */}
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(validCurrentPage + 1)}
                        disabled={validCurrentPage === validTotalPages}
                    >
                        <ChevronRight className="w-4 h-4" />
                    </Button>

                    {/* Last page */}
                    {showFirstLast && validCurrentPage < validTotalPages && (
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(validTotalPages)}
                            disabled={validCurrentPage === validTotalPages}
                        >
                            <ChevronsRight className="w-4 h-4" />
                        </Button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Pagination;
