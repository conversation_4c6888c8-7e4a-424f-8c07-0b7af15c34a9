"use client";
import { useState, useEffect } from "react";

const STORAGE_KEYS = {
  STEAM_IDS: 'sellcoin_steam_ids'
};

export function useFormStorage() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [steamIds, setSteamIds] = useState([]);

  // Helper function to safely get from localStorage
  const getStoredValue = (key) => {
    if (typeof window === 'undefined') return null;
    try {
      const value = localStorage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.warn('Error reading from localStorage:', error);
      return null;
    }
  };

  // Helper function to safely set to localStorage
  const setStoredValue = (key, value) => {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn('Error writing to localStorage:', error);
    }
  };

  // Load stored data on mount
  useEffect(() => {
    const storedSteamIds = getStoredValue(STORAGE_KEYS.STEAM_IDS) || [];
    setSteamIds(storedSteamIds);
    setIsLoaded(true);
  }, []);

  // Add new Steam ID
  const addSteamId = (steamId) => {
    if (!steamId || steamIds.includes(steamId)) return;

    const updatedSteamIds = [...steamIds, steamId];
    setSteamIds(updatedSteamIds);
    setStoredValue(STORAGE_KEYS.STEAM_IDS, updatedSteamIds);
  };

  // Remove Steam ID
  const removeSteamId = (steamId) => {
    const updatedSteamIds = steamIds.filter(id => id !== steamId);
    setSteamIds(updatedSteamIds);
    setStoredValue(STORAGE_KEYS.STEAM_IDS, updatedSteamIds);
  };

  // Clear all data
  const clearAllData = () => {
    setSteamIds([]);
    setStoredValue(STORAGE_KEYS.STEAM_IDS, []);
  };

  return {
    isLoaded,
    steamIds,
    addSteamId,
    removeSteamId,
    clearAllData
  };
}
