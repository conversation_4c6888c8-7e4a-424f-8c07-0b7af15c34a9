"use client";
import { motion } from "framer-motion";
import { CheckCircle2, <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { useEffect, useState } from "react";

export function SuccessCheckmark({ size = "default", className = "" }) {
  const sizeClasses = {
    sm: "w-8 h-8",
    default: "w-16 h-16",
    lg: "w-24 h-24",
    xl: "w-32 h-32"
  };

  return (
    <motion.div
      initial={{ scale: 0, rotate: -180 }}
      animate={{ scale: 1, rotate: 0 }}
      transition={{
        type: "spring",
        stiffness: 260,
        damping: 20,
        duration: 0.6
      }}
      className={`${sizeClasses[size]} ${className}`}
    >
      <motion.div
        initial={{ pathLength: 0 }}
        animate={{ pathLength: 1 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="relative"
      >
        <CheckCircle2 className="w-full h-full text-green-500" />
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: [0, 1.2, 1] }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="absolute inset-0 bg-green-500/20 rounded-full"
        />
      </motion.div>
    </motion.div>
  );
}

export function ConfettiAnimation() {
  const [particles, setParticles] = useState([]);

  useEffect(() => {
    const newParticles = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      rotation: Math.random() * 360,
      color: ['#3b82f6', '#8b5cf6', '#ec4899', '#10b981', '#f59e0b'][Math.floor(Math.random() * 5)]
    }));
    setParticles(newParticles);
  }, []);

  return (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          initial={{
            x: `${particle.x}%`,
            y: `${particle.y}%`,
            rotate: particle.rotation,
            scale: 0,
            opacity: 1
          }}
          animate={{
            y: "120%",
            rotate: particle.rotation + 360,
            scale: [0, 1, 0],
            opacity: [1, 1, 0]
          }}
          transition={{
            duration: 3,
            ease: "easeOut",
            delay: Math.random() * 0.5
          }}
          className="absolute w-2 h-2 rounded-full"
          style={{ backgroundColor: particle.color }}
        />
      ))}
    </div>
  );
}

export function PaymentSuccessAnimation({ onComplete }) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className="relative flex flex-col items-center justify-center p-8 text-center"
    >
      <ConfettiAnimation />
      
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
        className="relative z-10"
      >
        <SuccessCheckmark size="xl" className="mb-6" />
        
        <motion.h2
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.4 }}
          className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-4"
        >
          Thanh toán thành công!
        </motion.h2>
        
        <motion.p
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="text-lg text-gray-600 dark:text-gray-400 mb-8"
        >
          Đơn hàng của bạn đã được xử lý thành công
        </motion.p>
        
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="flex items-center justify-center space-x-2 text-green-600 dark:text-green-400"
        >
          <Sparkles className="h-5 w-5" />
          <span className="font-medium">Coin đã được cộng vào tài khoản</span>
          <Sparkles className="h-5 w-5" />
        </motion.div>
      </motion.div>
    </motion.div>
  );
}

export function FloatingStars() {
  const stars = Array.from({ length: 5 }, (_, i) => ({
    id: i,
    delay: i * 0.2,
    x: 20 + (i * 15),
    y: 20 + (i % 2) * 10
  }));

  return (
    <div className="absolute inset-0 pointer-events-none">
      {stars.map((star) => (
        <motion.div
          key={star.id}
          initial={{ opacity: 0, scale: 0, rotate: 0 }}
          animate={{
            opacity: [0, 1, 0],
            scale: [0, 1, 0],
            rotate: [0, 180, 360],
            y: [0, -20, -40]
          }}
          transition={{
            duration: 2,
            delay: star.delay,
            repeat: Infinity,
            repeatDelay: 3
          }}
          className="absolute"
          style={{ left: `${star.x}%`, top: `${star.y}%` }}
        >
          <Star className="h-4 w-4 text-yellow-400 fill-current" />
        </motion.div>
      ))}
    </div>
  );
}

export function RippleEffect({ trigger = false }) {
  return (
    <motion.div
      initial={{ scale: 0, opacity: 0.8 }}
      animate={trigger ? { scale: 4, opacity: 0 } : { scale: 0, opacity: 0.8 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="absolute inset-0 bg-green-500/20 rounded-full pointer-events-none"
    />
  );
}
