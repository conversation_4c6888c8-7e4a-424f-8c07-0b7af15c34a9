
import CardInfo from "@/components/ui/card-info";
import { Coins, MessageSquareText, Phone, ArrowUp, ArrowDown, Facebook } from "lucide-react";
export default function Rate({ rateSell, rateBuy, facebook, phone, type }) {
  return (
    <CardInfo className="p-6 bg-gradient-to-b from-purple-50 to-pink-50 dark:from-purple-600 dark:to-pink-500 text-gray-800 dark:text-white rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300">
      <div className="flex flex-col space-y-6">
        {/* Tiêu đề */}
        <div className="flex items-center space-x-3">
          <Coins className="text-purple-600 dark:text-yellow-400" size={32} />
          <h2 className="text-3xl font-extrabold tracking-wide text-gray-900 dark:text-white">
            MUA BÁN ITEM CSGO, COIN, ETOP
          </h2>
        </div>

        {/* L<PERSON>i kêu gọi hành động */}
        <div className="bg-gray-100/80 dark:bg-white/10 p-4 rounded-lg backdrop-blur-sm">
          <div className="flex items-center space-x-2">
            <MessageSquareText size={24} className="text-blue-600 dark:text-cyan-300" />
            <p className="text-sm font-medium">Bán coin ngay: inbox</p>
            <a href={facebook?.value} target="_blank" className="text-blue-600 dark:text-cyan-300 hover:text-blue-500 dark:hover:text-cyan-200 transition-all font-bold">
              Facebook BÙI HOÀNG
            </a>
          </div>
        </div>

        {/* Phần tỷ giá */}
        <div className="space-y-4">
          <div className="bg-green-100/70 dark:bg-green-500/20 p-4 rounded-lg">
            <div className="flex items-center space-x-2">
              <ArrowDown size={24} className="text-green-700 dark:text-green-300" />
              <span className="font-medium text-lg">Rate mua:</span>
              <span className="text-xl font-bold">{rateBuy}</span>
            </div>
          </div>
          <div className="bg-red-100/70 dark:bg-red-500/20 p-4 rounded-lg">
            <div className="flex items-center space-x-2">
              <ArrowUp size={24} className="text-red-700 dark:text-red-300" />
              <span className="font-medium text-lg">Rate bán:</span>
              <span className="text-xl font-bold">{rateSell}</span>
            </div>
          </div>
        </div>

        {/* Thông tin liên hệ */}
        <div className="space-y-2">
          <div className="text-sm font-medium mb-2 text-gray-600 dark:text-gray-300">Hỗ trợ khách hàng:</div>
          <div className="flex flex-col space-y-2">
            <a href={facebook?.value} target="_blank" rel="noopener noreferrer"
               className="flex items-center space-x-2 bg-gray-100/70 dark:bg-white/10 p-3 rounded-lg hover:bg-gray-200/50 dark:hover:bg-white/20 transition-all">
              <Facebook size={20} className="text-blue-600 dark:text-white" />
              <span className="text-gray-700 dark:text-white">Fanpage/Facebook</span>
            </a>
            <a href={`tel:${phone}`}
               className="flex items-center space-x-2 bg-gray-100/70 dark:bg-white/10 p-3 rounded-lg hover:bg-gray-200/50 dark:hover:bg-white/20 transition-all">
              <Phone size={20} className="text-gray-700 dark:text-white" />
              <span className="text-gray-700 dark:text-white">Phone/Zalo: {phone}</span>
            </a>
          </div>
        </div>

        {/* Lời cảm ơn */}
        <div className="py-3 mt-4 text-xl text-gray-600 dark:text-white/80">
          {type === "empire" ? (
            <p>
              Muốn mua shard etop?{' '}
              <a href="/etopfun" target="_blank" rel="noopener noreferrer"
                 className="font-bold text-purple-600 dark:text-yellow-400 hover:text-purple-500 dark:hover:text-yellow-300 transition-all underline">
                Mua ngay
              </a>
            </p>
          ) : type === "etop" ? (
            <p>
              Muốn mua coin csgoempire?{' '}
              <a href="/" target="_blank" rel="noopener noreferrer"
                 className="font-bold text-purple-600 dark:text-yellow-400 hover:text-purple-500 dark:hover:text-yellow-300 transition-all underline">
                Mua ngay
              </a>
            </p>
          ) : (
            <p>
              Muốn mua shard etop?{' '}
              <a href="/etopfun" target="_blank" rel="noopener noreferrer"
                 className="font-bold text-purple-600 dark:text-yellow-400 hover:text-purple-500 dark:hover:text-yellow-300 transition-all underline">
                Mua ngay
              </a>
            </p>
          )}
          <span className="italic">Cảm ơn quý khách!</span>
        </div>
      </div>
    </CardInfo>
  );
}
