"use client";
import { useState, useEffect } from "react";

export function useCoinCalculation(buyRate = 0.85) {
  const [coin, setCoin] = useState("");
  const [vnd, setVnd] = useState("");

  // Calculate VND when coin changes
  useEffect(() => {
    if (coin) {
      const coinValue = parseFloat(coin);
      if (!isNaN(coinValue)) {
        const calculatedVnd = Math.round(coinValue * buyRate * 1000); // Assuming 1 coin = 1000 VND base rate
        setVnd(calculatedVnd.toString());
      }
    } else {
      setVnd("");
    }
  }, [coin, buyRate]);

  return { coin, vnd, setCoin, setVnd };
}
