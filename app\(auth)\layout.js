
import "@/app/globals.css";
import { ThemeProvider } from "@/components/providers/theme-provider"
import Image from 'next/image'
import bgPic1 from '@/public/images/bg-dark.avif'
import bgPic2 from '@/public/images/bg-light.avif'

 
export const metadata= {
  title: 'Đăng nhập',
  description: '',
}
export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`antialiased min-h-screen flex flex-col`}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <div className="absolute z-20 top-0 inset-x-0 flex justify-center overflow-hidden pointer-events-none">
            <div className="w-[108rem] flex-none flex justify-end">
              <div className="flex-none max-w-none dark:hidden">
                <Image src={bgPic1} alt="">
                </Image>
              </div>             
              <div className="flex-none max-w-none hidden dark:block" >
                <Image src={bgPic2} alt="">
                </Image>
              </div>
            </div>
          </div>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
