import { AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import Link from "next/link";
import { <PERSON>, CardContent, CardHeader, CardFooter, CardTitle } from "@/components/ui/card";
export function ErrorHome() {
    <div className="relative flex flex-1 flex-col items-center justify-center pb-16 pt-12">
        <div className="flex items-center justify-center min-h-scree">
            <Card className="w-[380px]">
                <CardHeader>
                    <div className="flex items-center justify-center">
                        <AlertTriangle className="h-12 w-12 text-red-500" />
                    </div>
                    <CardTitle className="text-center text-2xl font-bold">Lỗi Cơ Sở Dữ Liệu</CardTitle>
                </CardHeader>
                <CardContent>
                    <Alert variant="destructive">
                        <AlertTitle>Không thể kết nối đến cơ sở dữ liệu</AlertTitle>
                        <AlertDescription>
                            Chúng tôi đang gặp sự cố khi cố gắng kết nối với cơ sở dữ liệu. Vui lòng thử lại sau.
                        </AlertDescription>
                    </Alert>
                </CardContent>
                <CardFooter className="flex flex-col space-y-4">
                    <Button className="w-full">
                        <Link href="/">Thử lại</Link>
                    </Button>
                    <p className="text-center text-sm text-gray-600">
                        Nếu vấn đề vẫn tiếp diễn, vui lòng liên hệ với đội ngũ hỗ trợ của chúng tôi.
                    </p>
                </CardFooter>
            </Card>
        </div>
    </div>
}