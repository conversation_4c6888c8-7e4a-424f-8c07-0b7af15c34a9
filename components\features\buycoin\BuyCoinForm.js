"use client";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useFormState } from "react-dom";
import { useEffect, useState, useCallback } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TrendingDown } from "lucide-react";
import { Dialog, DialogContent } from "@/components/ui/dialog";

import { buyCoinSchema } from "@/validations/buycoin.schema";
import { buyCoinAction } from "@/actions/buycoin";
import BuyCoinOrderInfo from "./BuyCoinOrderInfo";

// Import custom components
import SubmitButton from "./SubmitButton";
import BankSelector from "./BankSelector";
import CoinInput from "./CoinInput";
import AccountInfo from "./AccountInfo";
import FormMessages from "./FormMessages";
import SteamIdSelector from "./SteamIdSelector";
import ClearStorageButton from "./ClearStorageButton";

// Import custom hooks
import { useCoinCalculation } from "./hooks/useCoinCalculation";
import { useAccountLookup } from "./hooks/useAccountLookup";
import { useSharedSteamIds } from "@/hooks/useSharedSteamIds";
import { useFormStorage } from "./hooks/useFormStorage";

export default function BuyCoinForm({ buyRate }) {
  const [state, formAction] = useFormState(buyCoinAction, null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [lastOrderId, setLastOrderId] = useState(null);

  const {
    register,
    formState: { errors },
    setValue,
    reset
  } = useForm({
    resolver: zodResolver(buyCoinSchema),
  });

  const { coin, vnd, setCoin } = useCoinCalculation(buyRate);
  const {
    selectedBank,
    setSelectedBank,
    accountNumber,
    setAccountNumber,
    accountName,
    loadingAccountName
  } = useAccountLookup();

  // Shared Steam IDs hook
  const {
    isLoaded: steamIdsLoaded,
    steamIds,
    addSteamId,
    removeSteamId,
    clearAllData: clearSteamIds
  } = useSharedSteamIds();

  // Bank info storage hook
  const {
    isLoaded: bankInfoLoaded,
    bankInfo,
    saveBankInfo,
    clearBankData
  } = useFormStorage();

  const isLoaded = steamIdsLoaded && bankInfoLoaded;

  // Combined clear function
  const clearAllData = () => {
    clearSteamIds();
    clearBankData();
  };

  const handleModalClose = useCallback(() => {
    setIsModalOpen(false);
    // Reset form state to allow new orders to show popup
    formAction(null);
    // Optionally reset form fields for new order
    reset();
    setCoin('');
  }, [formAction, reset, setCoin]);

  // Open modal when order is created successfully
  useEffect(() => {
    if (state?.success && state?.data?.orderId) {
      // Only open modal for new orders (different orderId)
      if (state.data.orderId !== lastOrderId) {
        setIsModalOpen(true);
        setLastOrderId(state.data.orderId);
      }
    }
  }, [state?.success, state?.data?.orderId, lastOrderId]);

  // Load stored bank info when component mounts and storage is loaded
  useEffect(() => {
    if (isLoaded && bankInfo) {
      // Set stored bank values to form and state
      if (bankInfo.bankCode) {
        setSelectedBank(bankInfo.bankCode);
      }
      if (bankInfo.accountNumber) {
        setAccountNumber(bankInfo.accountNumber);
      }
    }
  }, [isLoaded, bankInfo, setSelectedBank, setAccountNumber]);

  // Handle bank selection with auto-save
  const handleBankSelect = (bankCode) => {
    setSelectedBank(bankCode);
    // Auto-save bank info when both bank and account are available
    if (bankCode && accountNumber) {
      saveBankInfo(bankCode, accountNumber);
    }
  };

  // Handle account number change with auto-save
  const handleAccountNumberChange = (value) => {
    setAccountNumber(value);
    // Auto-save bank info when both bank and account are available
    if (selectedBank && value) {
      saveBankInfo(selectedBank, value);
    }
  };

  // Handle clear all data
  const handleClearStorage = () => {
    clearAllData();
    // Reset form values
    setValue('steamId', '');
    setSelectedBank('');
    setAccountNumber('');
  };

  // Check if there's any stored data
  const hasStoredData = steamIds.length > 0 || bankInfo;

  return (
    <>
      <Card className="border-0  shadow-xl bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm">
      <CardHeader className="pb-3 md:pb-4 p-2 md:p-4">
        <CardTitle className="flex items-center space-x-2 md:space-x-3 text-xl md:text-2xl bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
          <TrendingDown className="h-6 w-6 md:h-7 md:w-7 text-orange-600" />
          <span>Bán Coin Cho Hệ Thống</span>
        </CardTitle>
        
      </CardHeader>

      <CardContent className="p-4 md:p-6 pt-0">
        <ClearStorageButton
          onClear={handleClearStorage}
          hasStoredData={hasStoredData}
        />

        <form action={formAction} className="space-y-4 md:space-y-6">
          {/* Steam ID Selector */}
          <SteamIdSelector
            steamIds={steamIds}
            onAddSteamId={addSteamId}
            onRemoveSteamId={removeSteamId}
            register={register}
            errors={errors}
            state={state}
          />

          {/* Coin Input Component */}
          <CoinInput
            coin={coin}
            vnd={vnd}
            buyRate={buyRate}
            onCoinChange={setCoin}
            register={register}
            errors={errors}
            state={state}
          />

          {/* Bank Selection Component */}
          <BankSelector
            selectedBank={selectedBank}
            onBankSelect={handleBankSelect}
            register={register}
            errors={errors}
          />

          {/* Account Info Component */}
          <AccountInfo
            accountNumber={accountNumber}
            accountName={accountName}
            selectedBank={selectedBank}
            loadingAccountName={loadingAccountName}
            onAccountNumberChange={handleAccountNumberChange}
            register={register}
            errors={errors}
          />

          {/* Form Messages Component */}
          <FormMessages state={state} />

          {/* Submit Button */}
          <div className="pt-3 md:pt-4">
            <SubmitButton />
          </div>
        </form>
      </CardContent>
    </Card>

    {/* Buy Coin Order Dialog */}
    <Dialog open={isModalOpen} onOpenChange={handleModalClose}>
      {state?.success && (
        <DialogContent className="max-w-2xl w-full max-h-[90vh] p-0 border-0 overflow-y-auto">
          <BuyCoinOrderInfo data={state?.data} />
        </DialogContent>
      )}
    </Dialog>
    </>
  );
}


