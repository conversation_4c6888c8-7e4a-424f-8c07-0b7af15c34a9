"use client";
import { useEffect } from "react";

export default function TawkTo() {
  useEffect(() => {
    const tawkto = () => {
      var Tawk_API = Tawk_API || {}, Tawk_LoadStart = new Date();
      (function () {
        var s1 = document.createElement("script"), s0 = document.getElementsByTagName("script")[0];
        s1.async = true;
        s1.src = 'https://embed.tawk.to/6706363acec6d0125df3b101/1i9o47lp1';
        s1.charset = 'UTF-8';
        s1.setAttribute('crossorigin', '*');
        s0.parentNode.insertBefore(s1, s0);
      })();
    };
    tawkto();
  }, []); // Empty dependency array to run the script once when the component is mounted

  return null; // No JSX to render for this component
}