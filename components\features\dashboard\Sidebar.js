
import Link from "next/link"
import { <PERSON>, CreditCard, LogOut, Setting<PERSON>, User, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import LogoutButton from "@/components/features/auth/LogoutButton"
export default function Sidebar({userInfo}) {
  const userMenu = [
    { name: "<PERSON><PERSON><PERSON> k<PERSON>n", href: "/user/info", icon: User },
    { name: "Đơn hàng", href: "/user/orders", icon: CreditCard },
    { name: "Thô<PERSON> báo", href: "/user/notifications", icon: Bell, },
    { name: "Cài đặt", href: "/user/settings", icon: Settings },
  ]
  return (
    <div className="hidden md:block left-0 z-50 w-64 p-6 shadow-lg ease-in-out md:relative md:translate-x-0">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <div className="ml-3">
            <p className="font-medium">{userInfo.username}</p>
            <p className="text-sm text-gray-500">{userInfo?.email || userInfo.steamId}</p>
          </div>
        </div>
      </div>
      <nav className="space-y-2">
        {userMenu.map(link => (
          <Link key={link.href} href={link.href}>
            <Button variant="ghost" className="w-full justify-start ">
              <link.icon className="mr-2 h-4 w-4" />
              <span>{link.name}</span>
            </Button>
          </Link>
        ))}
      </nav>
      <Separator className="my-4" />
      <LogoutButton />
    </div>
  )
}