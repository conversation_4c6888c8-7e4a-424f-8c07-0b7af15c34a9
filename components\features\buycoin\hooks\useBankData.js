"use client";
import { useState, useEffect } from "react";

export function useBankData() {
  const [banks, setBanks] = useState([]);
  const [loadingBanks, setLoadingBanks] = useState(true);

  // Fetch banks using internal API
  useEffect(() => {
    const fetchBanks = async () => {
      try {
        const response = await fetch('/api/banks');
        const result = await response.json();

        if (result.success && result.data) {
          setBanks(result.data);
        }
      } catch (error) {
        console.error('Error fetching banks:', error);
      } finally {
        setLoadingBanks(false);
      }
    };

    fetchBanks();
  }, []);

  return { banks, loadingBanks };
}
