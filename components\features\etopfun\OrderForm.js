"use client"

import { orderEtopfun } from "@/actions/order"
import { useState, useEffect, useCallback } from "react"
import { Input } from "@/components/ui/input"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useFormStatus } from "react-dom"
import { Button } from "@/components/ui/button"
import { useFormState } from "react-dom"
import { Label } from "@/components/ui/label"
import { orderSchemaEtop } from "@/validations/orderEtopfun.schema"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import OrderInfo from "@/components/features/orderinfo/OrderInfo"
import { fetchUserEtopfun } from "@/lib/http"

function SubmitButton() {
  const { pending } = useFormStatus()
  return (
    <Button type="submit" className="w-full" disabled={pending}>
      {pending ? "Đang mua..." : "Mua"}
    </Button>
  )
}

export default function OrderForm({ selectedItems, steamId, totalValue, banks, rate }) {
  const defaultBankNo = banks.length > 0 ? banks[0].bankNo : ""
  const [nickName, setNickname] = useState('');
  const createOrder = orderEtopfun.bind(null, selectedItems)
  const [state, formAction] = useFormState(createOrder, null)
  const [selectedBank, setSelectedBank] = useState(defaultBankNo)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const {
    register,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(orderSchemaEtop),
  })
  const handleModalClose = useCallback(() => {
    setIsModalOpen(false)
    formAction(null)
  }, [formAction])

  useEffect(() => {
    if (state?.success) {
      setIsModalOpen(true)
    }
  }, [state?.success])

  const setId = async (steamId) => {
    setNickname('loading...');
    const data = await fetchUserEtopfun(steamId);
    if (data) {
      setNickname(data.name);
    } else {
      setNickname("")
    }
  }
  useEffect(() => {
    if (steamId) {
      setId(steamId);
    }
  }, [steamId]);
  let amount = 0
  const coin100 = Math.round(totalValue * 100)
  if (coin100 < 1000) {
    amount = coin100 * 10 * rate
  } else amount = Math.round(coin100 * rate / 100.0) * 1000

  return (
    <>
      <div className="mt-4">
        <div className="text-orange-500">
          Value: {totalValue.toFixed(2)} Giá tiền: {amount.toLocaleString()} VND
        </div>
        <form action={formAction} className="flex max-w flex-col gap-4">
          <div className="space-y-2">
            <Label htmlFor="steamId">SteamId</Label>
            <Input id="steamId"
              defaultValue={steamId}
              type="text" {...register("steamId")}
              onBlur={(e) => setId(e.target.value)}
              placeholder="Enter your steamId" />
            {errors.steamId && <p className="text-sm text-red-500">{errors.steamId.message}</p>}
            {state?.errors?.steamId && <p className="text-sm text-red-500">{state.errors.steamId[0]}</p>}
          </div>


          <div className="space-y-2">
            <Label htmlFor="coin">NickName</Label>
            <Input type="text" {...register("nickName")} value={nickName} required />
            {errors.nickName && <p className="text-sm text-red-500">{errors.nickName.message}</p>}
            {state?.errors?.nickName && <p className="text-sm text-red-500">{state.errors.nickName[0]}</p>}
          </div>


          <div className="space-y-2">
            <Label htmlFor="bankNo">Ngân hàng</Label>
            <Select {...register("bankNo")} value={selectedBank} onValueChange={setSelectedBank}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn ngân hàng" />
              </SelectTrigger>
              <SelectContent>
                {banks.map((bank) => (
                  <SelectItem disabled={!bank.active} key={bank.bankNo} value={bank.bankNo}>
                    {bank.bankName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.bankNo && <p className="text-sm text-red-500">{errors.bankNo.message}</p>}
            {state?.errors?.bankNo && <p className="text-sm text-red-500">{state.errors.bankNo[0]}</p>}
          </div>
          {state?.errorServer && <p className="text-sm text-red-500">{state.message}</p>}
          <SubmitButton />
        </form>

      </div>
      <Dialog open={isModalOpen} onOpenChange={handleModalClose}>
        {state?.success && <DialogContent className="w-[90%] max-w-lg sm:max-w-xl md:max-w-3xl lg:max-w-5xl sm:rounded-lg rounded-none sm:h-auto h-screen  w-screen p-4">
          <DialogHeader>
            <DialogTitle>Thanh toán cho đơn hàng {state?.data?.content}</DialogTitle>
          </DialogHeader>
          <div className="max-h-full overflow-y-auto">
            <OrderInfo data={state?.data} />
          </div>
        </DialogContent>}
      </Dialog>
    </>

  )
}

