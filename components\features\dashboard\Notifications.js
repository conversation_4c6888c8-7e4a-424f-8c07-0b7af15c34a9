import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
export default function Notifications({notifications}) {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Thông báo</CardTitle>
                <CardDescription><PERSON><PERSON><PERSON> cập nhật hoạt động tài khoản của bạn</CardDescription>
            </CardHeader>
            <CardContent>
                <ul className="space-y-4">
                    {notifications.map((notification) => (
                        <li key={notification.id} className="flex justify-between items-start">
                            <div>
                                <p>{notification.message}</p>
                                <p className="text-sm text-gray-500">{notification.date}</p>
                            </div>
                            <Button variant="ghost" size="sm">Đã đọc</Button>
                        </li>
                    ))}
                </ul>
            </CardContent>
        </Card>
    )
}