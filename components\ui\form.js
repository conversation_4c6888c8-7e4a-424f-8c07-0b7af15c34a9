'use client';

import * as Form from '@radix-ui/react-form';
import { twx, cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import * as React from "react";
import { useFormState, useFormStatus } from "react-dom";



const FormControl = Form.Control;
const FormValidityState = Form.ValidityState;


const FormMessage = twx(Form.Message)`text-sm font-medium text-destructive`;
FormMessage.displayName = 'FormMessage';


const FormContext = React.createContext({
    fieldErrors: {},
    serverError: '',
    message: "",
    data: {},
    success: false
}
);


const FormRoot = React.forwardRef(
    ({ action, ...props }, ref) => {
        const [state, formAction] = useFormState(action, {
            fieldErrors: {},
            serverError: '',
            message: '',
            data: {},
            success: false
        });
        return (
            <FormContext.Provider value={state}>
                <Form.Root {...props} action={formAction} ref={ref} />
            </FormContext.Provider>
        );
    },
);
FormRoot.displayName = 'FormRoot'

const FormField = React.forwardRef(({ className, children, ...props }, ref) => {
    const state = React.useContext(FormContext);
    return (
        <Form.Field
            {...props}
            ref={ref}
            className={cn('space-y-2', className)}
            serverInvalid={Boolean(state?.fieldErrors[props.name])}
        >
            {children}
            {state?.fieldErrors[props.name]?.map((error) => (
                <FormMessage key={error}>{error}</FormMessage>
            ))}
        </Form.Field>
    );
});
FormField.displayName = 'FormField';

const FormLabel = React.forwardRef(({ className, ...props }, ref) => {

    return (
        <Form.Label asChild>
            <Label
                {...props}
                ref={ref}
                className={cn('data-[invalid]:text-destructive', className)}
            />
        </Form.Label>
    );
});
FormLabel.displayName = 'FormLabel';

const FormSubmit = React.forwardRef(({ className, children, ...props }, ref) => {
    const status = useFormStatus();
    const state = React.useContext(FormContext);
    return (
        <Form.Submit asChild>
            <Button  {...props} ref={ref} className={cn('mt-4 w-full', className)} disabled={status.pending}>
                {status.pending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {children}
            </Button>
        </Form.Submit>
    );
});

FormSubmit.displayName = 'FormSubmit';

const FormServerError = () => {
    const state = React.useContext(FormContext);
    if (!state?.serverError) return null;
    return (
        <div className="mt-2 text-sm font-medium text-destructive">
            {state.message}
        </div>
    );
};

const FormPopUp = () => {
    const state = React.useContext(FormContext);
    if (!state?.serverError) return null;
    return (
        <div className="mt-2 text-sm font-medium text-destructive">
            {state.message}
        </div>
    );
};
FormServerError.displayName = 'FormServerError';
export {
    FormRoot,
    FormField,
    FormLabel,
    FormControl,
    FormMessage,
    FormValidityState,
    FormServerError,
    FormSubmit,
}; 