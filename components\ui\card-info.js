export default function CardInfo({ title, children }) {
    return (
      <div className="max-w-8xl mx-auto px-2 pb-6 sm:px-6 md:px-8 xl:px-12">
        <div className="relative">
          <div className="mt-4 -mb-3">
            <div
              className="not-prose relative bg-white dark:bg-gray-900 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 antialiased"
              style={{ marginRight: 0 }}
            >
              <div className="relative rounded-xl overflow-auto md:p-8 p-2 py-3">
                {/* Title Section */}
                {title && (
                  <div className="border-b border-gray-200 dark:border-gray-700 mb-4">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {title}
                    </h2>
                  </div>
                )}
  
                {/* Content Section */}
                <div className="grid gap-2 text-lg text-gray-700 dark:text-gray-300 decoration-2 leading-relaxed">
                  {children}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }