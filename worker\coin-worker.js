
import {
    convertCoin<PERSON>mount, filterNewPositiveTransactions, filterNewTransactions,
    getTransactionDetails, getUserTransactions,
    isRecentTransaction, validateApiConfig
} from '../service/csgoempire-service.js';
import {
    sendCoinDepositNotification,
    sendTransactionPendingNotification,
    sendOrderFoundNotification,
    sendTransactionExpiredNotification
} from '../service/telegram-service.js';
import {
    findMatchingBuyCoinOrder,
    saveTransactionAndUpdateBuyCoinOrder,
    updateNotificationStatus
} from '../service/transaction-service.js';
import {
    savePendingTransaction,
    getPendingTransactions, getExpiredPendingTransactions,
    markTransactionAsMatched, markTransactionAsExpired,
    updatePendingTransactionNotification,
    isNewTransaction
} from '../service/pending-transaction-service.js';
import { updateBuyCoinOrder } from '@/service/buy-coin-service.js';


class CoinMonitorWorker {
    constructor() {
        this.isRunning = false;
        this.timeoutId = null;
        this.intervalMs = 20000; // 20 seconds
        this.lastCheckTime = null;
        this.errorCount = 0;
        this.maxErrors = 10;
        this.previousTransactions = []; // Store previous transactions for comparison
        this.stats = {
            totalChecks: 0,
            transactionsFound: 0,
            notificationsSent: 0,
            errors: 0,
            startTime: null
        };
    }

    /**
     * Start the worker
     * @returns {Promise<Object>} Start result
     */
    async start() {
        try {
            if (this.isRunning) {
                return {
                    success: false,
                    error: 'Worker is already running'
                };
            }

            // Validate configurations
            const apiValidation = await validateApiConfig();
            if (!apiValidation.isValid) {
                return {
                    success: false,
                    error: `CSGOEmpire API configuration invalid: ${apiValidation.errors.join(', ')}`
                };
            }


            this.isRunning = true;
            this.errorCount = 0;
            this.stats.startTime = new Date();
            this.lastCheckTime = Date.now();

            // Start the recursive timeout loop
            this.scheduleNextExecution();

            return {
                success: true,
                message: 'Worker started successfully',
                intervalMs: this.intervalMs
            };

        } catch (error) {
            console.error('Error starting coin monitor worker:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Stop the worker
     * @returns {Promise<Object>} Stop result
     */
    async stop() {
        try {
            if (!this.isRunning) {
                return {
                    success: false,
                    error: 'Worker is not running'
                };
            }

            this.isRunning = false;

            if (this.timeoutId) {
                clearTimeout(this.timeoutId);
                this.timeoutId = null;
            }

            // Calculate runtime
            const runtime = this.stats.startTime ?
                Math.round((Date.now() - this.stats.startTime.getTime()) / 1000) : 0;

            return {
                success: true,
                message: 'Worker stopped successfully',
                runtime,
                stats: this.stats
            };

        } catch (error) {
            console.error('Error stopping coin monitor worker:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get worker status
     * @returns {Object} Worker status
     */
    getStatus() {
        const runtime = this.stats.startTime ?
            Math.round((Date.now() - this.stats.startTime.getTime()) / 1000) : 0;

        return {
            isRunning: this.isRunning,
            intervalMs: this.intervalMs,
            lastCheckTime: this.lastCheckTime,
            errorCount: this.errorCount,
            maxErrors: this.maxErrors,
            runtime,
            stats: this.stats
        };
    }

    /**
     * Schedule the next execution using setTimeout
     */
    scheduleNextExecution() {
        if (!this.isRunning) return;

        this.timeoutId = setTimeout(async () => {
            await this.checkForNewTransactions();
            // Schedule next execution if still running
            if (this.isRunning) {
                this.scheduleNextExecution();
            }
        }, this.intervalMs);
    }

    /**
     * Main worker function - check for new transactions and pending transactions
     */
    async checkForNewTransactions() {
        try {
            this.stats.totalChecks++;
            this.lastCheckTime = Date.now();

            // First, check for expired pending transactions
            await this.checkExpiredPendingTransactions();

            // Then, check for pending transactions that might now have orders
            await this.checkPendingTransactions();

            // Finally, check for new transactions
            await this.checkNewTransactions();

            this.errorCount = 0;

        } catch (error) {
            this.errorCount++;
            this.stats.errors++;

            console.error('Error in checkForNewTransactions:', error);

            // Stop worker if too many errors
            if (this.errorCount >= this.maxErrors) {
                console.error(`Too many errors (${this.errorCount}), stopping worker`);
                await this.stop();
                return; // Don't schedule next execution
            }
        }
    }

    /**
     * Check for new transactions from CSGOEmpire
     */
    async checkNewTransactions() {
        try {
        

            // Get recent transactions
            const transactionsResult = await getUserTransactions(1, 100);
            if (!transactionsResult.success) {
              
                throw new Error(`Failed to fetch transactions: ${transactionsResult.error}`);
            }

            // Filter only new transactions compared to previous check
            const newTransactions = filterNewTransactions(transactionsResult.data, this.previousTransactions);

            // Update previous transactions for next comparison
            this.previousTransactions = transactionsResult.data;

            if (newTransactions.length === 0) {
           
                return;
            }

          

            // Filter positive transactions (incoming coins) from new transactions only
            const newPositiveTransactions = filterNewPositiveTransactions(newTransactions);

            if (newPositiveTransactions.length === 0) {
             
                return;
            }

           

            for (const transaction of newPositiveTransactions) {
                await this.processNewTransaction(transaction);
            }

        } catch (error) {
            console.error('Error in checkNewTransactions:', error);
            throw error;
        }
    }

    /**
     * Check pending transactions for matching orders
     */
    async checkPendingTransactions() {
        try {
          

            const pendingResult = await getPendingTransactions();
            if (!pendingResult.success) {
                console.error('Failed to get pending transactions:', pendingResult.error);
                return;
            }

            if (pendingResult.data.length === 0) {
                return;
            }

          

            for (const pendingTransaction of pendingResult.data) {
                await this.checkPendingTransactionForOrder(pendingTransaction);
            }

        } catch (error) {
            console.error('Error in checkPendingTransactions:', error);
            throw error;
        }
    }

    /**
     * Check for expired pending transactions (older than 5 minutes)
     */
    async checkExpiredPendingTransactions() {
        try {
            const expiredResult = await getExpiredPendingTransactions();
            if (!expiredResult.success) {
                console.error('Failed to get expired pending transactions:', expiredResult.error);
                return;
            }

            if (expiredResult.data.length === 0) {
                return;
            }

           

            for (const expiredTransaction of expiredResult.data) {
                await this.processExpiredTransaction(expiredTransaction);
            }

        } catch (error) {
            console.error('Error in checkExpiredPendingTransactions:', error);
            throw error;
        }
    }

    /**
     * Process a new transaction from CSGOEmpire
     * @param {Object} transaction - Transaction data
     */
    async processNewTransaction(transaction) {
        try {
            if (isRecentTransaction(transaction.timestamp_raw)) {
                return;
            }
            const transactionId = transaction.id;
            // Check if already in pending tracking
            const newCheck = await isNewTransaction(transactionId);
            if (!newCheck.success) {
                throw new Error(`Failed to check pending status: ${pendingCheck.error}`);
            }
            if (newCheck.isNew) {
                return; // Already being tracked
            }
          
            this.stats.transactionsFound++;
            // Get detailed transaction data
            const detailsResult = await getTransactionDetails(transactionId);
            if (!detailsResult.success) {
                throw new Error(`Failed to get transaction details: ${detailsResult.error}`);
            }

            const detailedTransaction = detailsResult.data;
            const steamId = detailedTransaction.data?.steam_id;
            const coinAmount = convertCoinAmount(detailedTransaction.delta);
          
            const matchResult = await findMatchingBuyCoinOrder(
                steamId,
                coinAmount,
                detailedTransaction.timestamp
            );

            if (matchResult.success && matchResult.hasMatch) {
                // Found matching order immediately
                const matchedOrderId = matchResult.data.order_id;
             

                // Save to processed transactions
                const saveResult = await saveTransactionAndUpdateBuyCoinOrder(detailedTransaction, matchedOrderId);
                if (!saveResult.success) {
                    throw new Error(`Failed to save processed transaction: ${saveResult.error}`);
                }

                // Send notification about found match
                const notificationResult = await sendCoinDepositNotification(
                    detailedTransaction,
                    matchResult.data
                );

                // Update notification status
                await updateNotificationStatus(transactionId, notificationResult.success);

                if (notificationResult.success) {
                    this.stats.notificationsSent++;
                } else {
                    console.error(`Failed to send notification for transaction ${transactionId}: ${notificationResult.error}`);
                }
            } else {
                // No matching order found, add to pending tracking
              

                const pendingResult = await savePendingTransaction(detailedTransaction);
                if (!pendingResult.success) {
                    throw new Error(`Failed to save pending transaction: ${pendingResult.error}`);
                }

                // Send notification about pending transaction
                const notificationResult = await sendTransactionPendingNotification(pendingResult?.data || detailedTransaction);

                // Update notification status for pending transaction
                await updatePendingTransactionNotification(transactionId, notificationResult.success);

                if (notificationResult.success) {
                    this.stats.notificationsSent++;
                } else {
                    console.error(`Failed to send pending notification for transaction ${transactionId}: ${notificationResult.error}`);
                }
            }

        } catch (error) {
            console.error(`Error processing new transaction ${transaction.id}:`, error);
            throw error;
        }
    }

    /**
     * Check a pending transaction for matching order
     * @param {Object} pendingTransaction - Pending transaction data
     */
    async checkPendingTransactionForOrder(pendingTransaction) {
        try {
            const transactionId = pendingTransaction.transaction_id;
            const steamId = pendingTransaction.steam_id;
            const coinAmount = pendingTransaction.coin_amount;
            const transactionTimestamp = pendingTransaction.transaction_timestamp;
        

            const matchResult = await findMatchingBuyCoinOrder(
                steamId,
                coinAmount,
                transactionTimestamp
            );

            if (matchResult.success && matchResult.hasMatch) {
                // Found matching order!
                const matchedOrderId = matchResult.data.order_id;
              

                // Mark as matched in pending table
                const data = await markTransactionAsMatched(transactionId, matchedOrderId);
                if (!data.success) {
                  
                    return;
                }
                const updateResult = await updateBuyCoinOrder(matchedOrderId, {
                    status: 'processing',
                    adminNote: `Auto-processed: Transaction ${transactionId} matched`,
                    processedBy: 'system_worker'
                });

                if (!updateResult.success) {
                    console.error(`Failed to update order ${matchedOrderId} to processing:`, updateResult.error);
                    // Continue with transaction tracking even if order update fails
                } else {
                   
                }
                const notificationResult = await sendOrderFoundNotification(data.data, matchResult.data);

                if (notificationResult.success) {
                    this.stats.notificationsSent++;
                } else {
                    console.error(`Failed to send order found notification for transaction ${transactionId}: ${notificationResult.error}`);
                }
            }

        } catch (error) {
            console.error(`Error checking pending transaction ${pendingTransaction.transaction_id}:`, error);
            throw error;
        }
    }

    /**
     * Process an expired pending transaction
     * @param {Object} expiredTransaction - Expired transaction data
     */
    async processExpiredTransaction(expiredTransaction) {
        try {
            const transactionId = expiredTransaction.transaction_id;
        
            // Mark as expired in pending table
            await markTransactionAsExpired(transactionId);

            // Send notification about expiration
            const transactionData = {
                id: transactionId,
                data: {
                    steam_id: expiredTransaction.steam_id,
                    display_name: 'Unknown'
                },
                delta: expiredTransaction.delta_amount,
                delta_coins: expiredTransaction.coin_amount,
                date: expiredTransaction.transaction_date
            };

            const notificationResult = await sendTransactionExpiredNotification(transactionData);

            if (notificationResult.success) {
                this.stats.notificationsSent++;
            } else {
                console.error(`Failed to send expired notification for transaction ${transactionId}: ${notificationResult.error}`);
            }

        } catch (error) {
            console.error(`Error processing expired transaction ${expiredTransaction.transaction_id}:`, error);
            throw error;
        }
    }
}

const globalForWorker = globalThis;
if (!globalForWorker.coinMonitorWorker) {
    globalForWorker.coinMonitorWorker = new CoinMonitorWorker();
}
const coinMonitorWorker = globalForWorker.coinMonitorWorker;
export default coinMonitorWorker;

