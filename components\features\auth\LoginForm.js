import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import Link from "next/link"
import { Heart, Bell, Users } from "lucide-react"
import steamIcon from "@/public/images/steam.svg"
import googleIcon from "@/public/images/google.png"
import discordIcon from "@/public/images/google.png" //

export default function LoginForm() {
  return (
    <div className="space-y-8">
      {/* Why Sign In section */}
      <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-6">
        <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
          Tại sao lại đăng nhập?
        </h3>
        <ul className="space-y-4">
          <li className="flex items-center text-gray-700 dark:text-gray-300">
            <Heart className="mr-3 text-blue-500 dark:text-blue-400" size={20} />
            <span>L<PERSON><PERSON> lịch sử đơn hàng</span>
          </li>
          <li className="flex items-center text-gray-700 dark:text-gray-300">
            <Heart className="mr-3 text-blue-500 dark:text-blue-400" size={20} />
            <span>Tự điền steam id</span>
          </li>
          <li className="flex items-center text-gray-700 dark:text-gray-300">
            <Heart className="mr-3 text-blue-500 dark:text-blue-400" size={20} />
            <span>Có rate mua đẹp hơn</span>
          </li>
        </ul>
      </div>

      {/* Login buttons */}
      <div className="space-y-3">
        {/* Steam Login Button */}
        <div className="w-full mb-4">
          <Link href="/login/steam">
            <Button
              variant="outline"
              className="w-full bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700 hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-900 dark:text-white h-12 flex justify-between items-center px-6"
            >
              <div className="flex items-center">
                <Image
                  src={steamIcon || "/placeholder.svg"}
                  height={24}
                  width={24}
                  alt="Steam"
                  className="mr-3"
                />
                <span>Tiếp tục với Steam</span>
              </div>
              <span className="text-xl">→</span>
            </Button>
          </Link>
        </div>

        {/* Google Login Button */}
        <div className="w-full">
          <Link href="/login/google">
            <Button
              variant="outline"
              className="w-full bg-red-500 dark:bg-red-600 border-red-400 dark:border-red-500 hover:bg-red-600 dark:hover:bg-red-700 text-white h-12 flex justify-between items-center px-6"
            >
              <div className="flex items-center">
                <Image
                  src={googleIcon || "/placeholder.svg"}
                  height={24}
                  width={24}
                  alt="Google"
                  className="mr-3"
                />
                <span>Tiếp tục với Google</span>
              </div>
              <span className="text-xl">→</span>
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}