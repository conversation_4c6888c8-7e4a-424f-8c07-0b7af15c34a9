
'use client'
import ItemBox from "@/components/features/etopfun/ItemBox";
export function ItemChoose({ selectedItems, onItemDeselect }) {
    return (
        <div className="space-y-2">
            <div className="flex items-center gap-2">
                <h2 className="text-lg font-semibold">Chọn Item</h2>
            </div>
            <div className="flex flex-wrap gap-2 overflow-auto custom-scrollbar max-h-52">
                {selectedItems.map((item) => (
                    <div key={item.id} onClick={() => onItemDeselect(item)} className="cursor-pointer">
                        <ItemBox
                            imageUrl={item.image}
                            imageBottomShow={item.imageBottomShow}
                            price={item.value}
                            status={item.status}
                        />
                    </div>
                ))}
            </div>
        </div>
    )

}