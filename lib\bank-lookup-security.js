import { headers } from 'next/headers';

// Security utilities for bank lookup API

export function getClientInfo() {
    const headersList = headers();
    
    return {
        ip: headersList.get('x-forwarded-for') || 
            headersList.get('x-real-ip') || 
            headersList.get('cf-connecting-ip') || // Cloudflare
            'unknown',
        userAgent: headersList.get('user-agent') || 'unknown',
        origin: headersList.get('origin'),
        referer: headersList.get('referer'),
        timestamp: new Date().toISOString()
    };
}

export function logSecurityEvent(event, details = {}) {
    const clientInfo = getClientInfo();
    
    console.log(`[BANK_LOOKUP_SECURITY] ${event}`, {
        ...clientInfo,
        ...details,
        timestamp: new Date().toISOString()
    });
}

export function isValidBankData(bankBin, accountNumber) {
    // Basic validation
    if (!bankBin || !accountNumber) {
        return { valid: false, error: 'Missing required fields' };
    }
    
    // Bank BIN should be 6 digits
    if (!/^\d{6}$/.test(bankBin)) {
        return { valid: false, error: 'Invalid bank BIN format' };
    }
    
    // Account number should be 6-20 digits
    if (!/^\d{6,20}$/.test(accountNumber)) {
        return { valid: false, error: 'Invalid account number format' };
    }
    
    return { valid: true };
}

// Rate limit key generators
export function getRateLimitKey(ip, type = 'general') {
    return `bank_lookup_rate_limit:${type}:${ip}`;
}

// Cache key generators
export function getCacheKey(bankBin, accountNumber) {
    return `bank_lookup:${bankBin}:${accountNumber}`;
}

// Security headers
export function getSecurityHeaders(origin) {
    return {
        'Access-Control-Allow-Origin': origin,
        'Access-Control-Allow-Credentials': 'true',
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin'
    };
}

// Suspicious activity detection
export function detectSuspiciousActivity(clientInfo, requestData) {
    const suspicious = [];
    
    // Check for rapid requests from same IP (this would be handled by rate limiting)
    // Check for unusual user agents
    if (clientInfo.userAgent.includes('bot') || 
        clientInfo.userAgent.includes('crawler') ||
        clientInfo.userAgent.includes('spider')) {
        suspicious.push('bot_user_agent');
    }
    
    // Check for missing standard headers
    if (!clientInfo.origin && !clientInfo.referer) {
        suspicious.push('missing_origin_headers');
    }
    
    // Check for unusual account number patterns
    if (requestData.accountNumber && /^(.)\1{5,}$/.test(requestData.accountNumber)) {
        suspicious.push('repetitive_account_number');
    }
    
    return suspicious;
}
