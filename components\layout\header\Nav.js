"use strict";

import Link from 'next/link';
import NavbarLogo from '@/components/layout/header/Logo';
import { NavLinkDesktop } from '@/components/layout/header/NavLink';
import { UserMenuDesktop } from '@/components/layout/sidebar/MenuUser';
import { ModeToggle } from '@/components/layout/header/ModeToggle';
import Sidebar from '@/components/layout/sidebar/Sidebar';
import { checkAuth } from '@/lib/utils';
import { cookies } from 'next/headers';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTrigger } from '@/components/ui/dialog';
import LoginForm from '@/components/features/auth/LoginForm';
import { DialogTitle } from '@/components/ui/dialog';
import Image from 'next/image';
import  banner from '@/public/images/banner.png'
export default async function NavBar() {
    const cookieStore = cookies();
    let { isLoggedIn, userInfo } = await checkAuth(cookieStore);
    return (
        <div className="sticky top-0 z-50 w-full backdrop-blur-xl supports-[backdrop-filter]:bg-background/95 border-b border-border">
            {/* Enhanced gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 via-purple-50/15 to-indigo-50/30 dark:from-blue-900/20 dark:via-purple-900/15 dark:to-indigo-900/20" />

            {/* Enhanced shadow with glow effect */}
            <div className="absolute inset-0 shadow-lg shadow-primary/5 dark:shadow-primary/10" />

            <div className="relative max-w-8xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="h-20 flex items-center justify-between">
                    {/* Left section - Enhanced Logo */}
                    <div className="flex items-center flex-1">
                        <Link
                            href="/"
                            className="group relative"
                        >
                            {/* Glow effect on hover */}
                            <div className="absolute -inset-2 bg-gradient-to-r from-primary via-purple-600 to-indigo-600 rounded-2xl blur-lg opacity-0 group-hover:opacity-15 dark:group-hover:opacity-25 transition-opacity duration-500"></div>

                            <div className="relative">
                                <NavbarLogo className="group-hover:scale-105 transition-transform duration-300" />
                            </div>
                        </Link>
                    </div>

                    {/* Middle section - Enhanced Desktop Navigation */}
                    <div className="hidden md:flex items-center justify-center flex-1">
                        <nav className="flex space-x-2 bg-background/80 backdrop-blur-md border border-border/50 rounded-full p-2 shadow-lg shadow-primary/5">
                            <NavLinkDesktop isLoggedIn={isLoggedIn} />
                        </nav>
                    </div>

                    {/* Right section - Enhanced Controls */}
                    <div className="flex items-center justify-end flex-1 space-x-3">
                        {/* Desktop Controls */}
                        <div className="hidden md:flex items-center space-x-3">
                            <div className="flex items-center space-x-3">
                                <div className="relative">
                                    <ModeToggle />
                                </div>

                                {isLoggedIn ? (
                                    <div className="relative">
                                        <UserMenuDesktop user={userInfo} />
                                    </div>
                                ) : (
                                    <Dialog>
                                        <DialogTrigger asChild>
                                            <button className="relative group px-6 py-3 rounded-full bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white font-semibold hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 overflow-hidden">
                                                <span className="relative z-10 flex items-center space-x-2">
                                                    <span>Đăng nhập</span>
                                                    <svg className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                                    </svg>
                                                </span>
                                                <div className="absolute inset-0 bg-gradient-to-r from-blue-700 via-purple-700 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                                <div className="absolute inset-0 bg-white/20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>
                                            </button>
                                        </DialogTrigger>
                                        <DialogContent className="max-w-5xl p-0 rounded-2xl border border-border overflow-hidden shadow-2xl">
                                            <div className="flex md:grid md:grid-cols-2">
                                                {/* Left side - Enhanced Image */}
                                                <div className="relative h-full overflow-hidden">
                                                    <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-purple-600/20 z-10"></div>
                                                    <Image
                                                        src={banner}
                                                        alt="Login background"
                                                        width={500}
                                                        height={600}
                                                        className="object-cover h-full w-full transform hover:scale-105 transition-transform duration-700"
                                                        style={{ clipPath: "polygon(0 0, 100% 0, 85% 100%, 0 100%)" }}
                                                    />
                                                    <div className="absolute bottom-6 left-6 z-20">
                                                        <div className="text-white text-3xl font-bold mb-2">
                                                            EmpireVN
                                                        </div>
                                                        <p className="text-white/80 text-lg">
                                                            Nền tảng giao dịch coin
                                                        </p>
                                                    </div>
                                                </div>

                                                {/* Right side - Enhanced Login form */}
                                                <div className="bg-background text-foreground p-8 relative">
                                                    <DialogHeader className="mb-8">
                                                        <DialogTitle className="text-3xl font-bold text-center gradient-text-primary">
                                                            Chào mừng quay lại!
                                                        </DialogTitle>
                                                        <DialogDescription className="text-center text-muted-foreground text-lg mt-3">
                                                            Đăng nhập để tiếp tục giao dịch
                                                        </DialogDescription>
                                                    </DialogHeader>

                                                    <LoginForm />
                                                </div>
                                            </div>
                                        </DialogContent>
                                    </Dialog>
                                )}
                            </div>
                        </div>

                        {/* Enhanced Mobile Menu */}
                        <div className="md:hidden">
                            <Sidebar user={userInfo} isLoggedIn={isLoggedIn} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}