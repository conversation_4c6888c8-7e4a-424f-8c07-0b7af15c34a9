"use strict";
"use client";
import { useState, useEffect, useRef, useCallback } from "react";
import BankInfo from "./BankInfo";
import QrCode from "@/components/common/Qrcode";
import OrderStatus from "./OrderStatus";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Clock, CreditCard, QrCode as QrCodeIcon, CheckCircle2, AlertCircle, User } from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

export default function OrderInfo({ data, steamInfo, bankInfo, orderId }) {
  // Thiết lập thời gian 5 phút (300 giây)
  const [timeLeft, setTimeLeft] = useState(300);
  const [activeTab, setActiveTab] = useState("qr");
  const [currentOrderData, setCurrentOrderData] = useState(null);
  const intervalRef = useRef(null);

  // Callback để nhận data từ OrderStatus
  const handleOrderDataUpdate = useCallback((orderData) => {
    setCurrentOrderData(orderData);
  }, []);

  useEffect(() => {
    if (!data && !orderId) return;

    intervalRef.current = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 0) {
          clearInterval(intervalRef.current);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [data, orderId]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const getTimeStatus = () => {
    if (timeLeft > 240) return { color: "bg-green-500", text: "Còn nhiều thời gian" };
    if (timeLeft > 120) return { color: "bg-yellow-500", text: "Sắp hết thời gian" };
    if (timeLeft > 0) return { color: "bg-orange-500", text: "Gấp rút thanh toán" };
    return { color: "bg-red-500", text: "Hết thời gian" };
  };

  const timeStatus = getTimeStatus();

  // Xử lý data từ props
  const orderData = data 

  // Kiểm tra nếu không có data
  if (!orderData || (!orderData.bank && !bankInfo)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 text-lg">Không thể tải thông tin thanh toán</p>
          <p className="text-gray-500 mt-2">Vui lòng thử lại sau</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 p-4">
      <div className="max-w-2xl mx-auto">
        {/* Header Section */}
        <div className="text-center mb-3">
          <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Thông Tin Thanh Toán
          </h1>
        </div>

        {/* Timer Section */}
        <Card className="mb-3 border-0 shadow-md bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm">
          <CardContent className="p-2">
            <div className="flex items-center justify-center space-x-3">
              <Clock className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Thời gian <span className="hidden md:inline">còn lại</span>
              </span>
              <div className={`w-2 h-2 rounded-full ${timeStatus.color} animate-pulse`}></div>
              <span className="text-lg font-bold text-gray-900 dark:text-white font-mono">
                {formatTime(timeLeft)}
              </span>
              <Badge variant={timeLeft > 120 ? "default" : "destructive"} className="text-xs px-2 py-0.5">
                {timeStatus.text}
              </Badge>
            </div>
            {timeLeft <= 0 && (
              <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-800">
                <div className="flex items-center space-x-2 text-red-700 dark:text-red-400">
                  <AlertCircle className="h-3 w-3 flex-shrink-0" />
                  <span className="font-semibold text-xs">⏰ Hết thời gian! Kiểm tra trạng thái bên dưới</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Steam Info Section */}
        {steamInfo && steamInfo.steamAvatar && steamInfo.steamName && (
          <Card className="mb-3 border-0 shadow-md bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm">
            <CardHeader className="p-2">
             
            </CardHeader>
            <CardContent className="p-2 pt-0">
              <div className="flex items-center space-x-3 p-1 md:p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200/50 dark:border-blue-700/30">
                <Avatar className="h-12 w-12 ring-2 ring-blue-200 dark:ring-blue-700">
                  <AvatarImage src={steamInfo.steamAvatar} alt={steamInfo.steamName} />
                  <AvatarFallback className="bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-300">
                    {steamInfo.steamName?.charAt(0)?.toUpperCase() || 'S'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="font-semibold text-gray-900 dark:text-gray-100">
                    {steamInfo.steamName}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Steam ID: {steamInfo.steamId}
                  </p>
                  {steamInfo.steamLevel && (
                    <p className="text-xs text-blue-600 dark:text-blue-400">
                      Level: {steamInfo.steamLevel}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Payment Methods Tabs */}
        <div className="mb-3">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2 mb-3">
              <TabsTrigger value="qr" className="flex items-center space-x-2">
                <QrCodeIcon className="h-4 w-4" />
                <span>Quét QR Code</span>
              </TabsTrigger>
              <TabsTrigger value="bank" className="flex items-center space-x-2">
                <CreditCard className="h-4 w-4" />
                <span>Chuyển Khoản</span>
              </TabsTrigger>
            </TabsList>

            <div>
              <TabsContent value="qr" className="mt-0">
                <Card className="border-0 shadow-lg bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm">
                  <CardHeader className="p-3 text-center">
                    <CardTitle className="flex items-center justify-center space-x-2 text-base text-gray-800 dark:text-gray-100">
                      <QrCodeIcon className="h-4 w-4 text-blue-600" />
                      <span>Quét Mã QR Thanh Toán</span>
                    </CardTitle>
                  </CardHeader>

                  <CardContent className="p-3 pt-0 flex flex-col items-center">
                    <div className="flex justify-center mb-4">
                      <div className="p-0 bg-white rounded-xl shadow-lg border-2 border-gray-200 dark:border-gray-600">
                        <div className="w-64 h-64 flex items-center justify-center">
                          <QrCode data={orderData} />
                        </div>
                      </div>
                    </div>

                    <div className="text-center space-y-1 hidden md:block">
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        <span className="font-semibold">Số tiền:</span> {Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(orderData.amount)}
                      </p>
                      <p className="text-sm text-gray-700 dark:text-gray-300">
                        <span className="font-semibold">Nội dung:</span> {orderData.content}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="bank" className="mt-0">
                <Card className="border-0 shadow-lg bg-gradient-to-br from-slate-100 via-blue-100 to-indigo-200 dark:from-slate-700 dark:via-slate-600 dark:to-slate-800 text-gray-800 dark:text-white overflow-hidden relative">
                  <div className="absolute top-0 right-0 w-16 h-16 bg-blue-200/30 dark:bg-slate-500/20 rounded-full -translate-y-8 translate-x-8"></div>
                  <div className="absolute bottom-0 left-0 w-12 h-12 bg-indigo-200/20 dark:bg-slate-600/15 rounded-full translate-y-6 -translate-x-6"></div>

                  <CardHeader className="relative z-10 p-3">
                    <CardTitle className="flex items-center space-x-2 text-base text-gray-800 dark:text-white">
                      <CreditCard className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                      <span>Thông Tin Chuyển Khoản</span>
                    </CardTitle>
                  </CardHeader>

                  <CardContent className="relative z-10 p-3 pt-0">
                    <BankInfo data={orderData} />

                    <div className="mt-3 p-2 bg-gradient-to-r from-green-100/60 to-emerald-100/60 dark:from-green-800/20 dark:to-emerald-800/20 rounded-lg backdrop-blur-sm border border-green-200/50 dark:border-green-700/30">
                      <div className="flex items-start space-x-2">
                        <CheckCircle2 className="h-3 w-3 mt-0.5 text-green-600 dark:text-green-400 flex-shrink-0" />
                        <p className="text-xs leading-relaxed text-gray-700 dark:text-gray-300">
                          <span className="font-semibold">Lưu ý:</span> Hệ thống tự động hoàn tất đơn hàng sau 5-30s. Nếu sau 5 phút chưa được cộng, liên hệ hỗ trợ.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        {/* Order Status Section */}
        <div className="mt-3">
          <Card className="border-0 shadow-md bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm">
            <CardHeader className="p-2">
              <CardTitle className="text-sm text-gray-800 dark:text-gray-100">
                Trạng Thái Đơn Hàng
              </CardTitle>
            </CardHeader>

            <CardContent className="p-2 pt-0">
              <OrderStatus orderId={orderData.orderId} onOrderDataUpdate={handleOrderDataUpdate} />
            </CardContent>
          </Card>
        </div>

        {/* Features Section - Minimal */}
        <div className="mt-3 mb-4 p-2 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/10 dark:to-pink-900/10 rounded-lg border border-purple-100 dark:border-purple-800/30">
          <div className="flex justify-center space-x-6 text-center">
            <div className="flex items-center space-x-1">
              <CheckCircle2 className="h-3 w-3 text-green-600 dark:text-green-400" />
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Tự Động</span>
            </div>

            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3 text-blue-600 dark:text-blue-400" />
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">5-30s</span>
            </div>

            <div className="flex items-center space-x-1">
              <CreditCard className="h-3 w-3 text-purple-600 dark:text-purple-400" />
              <span className="text-xs font-medium text-gray-700 dark:text-gray-300">An Toàn</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
