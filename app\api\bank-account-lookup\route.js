
import { NextResponse } from 'next/server';
import { getCache, setCache, checkRateLimit } from '@/lib/redis';
import {
    getClientInfo,
    logSecurityEvent,
    isValidBankData,
    getRateLimitKey,
    getCacheKey,
    getSecurityHeaders,
    detectSuspiciousActivity
} from '@/lib/bank-lookup-security';
import { getNameAccount } from '@/service/bank-service';
// Allowed origins for this API
const ALLOWED_ORIGINS = [
    'http://localhost:3000',
    'https://empirevn.store',
    'https://www.empirevn.store',
    process.env.REALM?.replace(/"/g, ''), // Remove quotes from env var
    process.env.NEXT_PUBLIC_APP_URL,
    ...(process.env.BANK_LOOKUP_ALLOWED_ORIGINS?.split(',') || [])
].filter(Boolean); // Remove undefined values

function checkOrigin(request) {
    const origin = request.headers.get('origin');
    const referer = request.headers.get('referer');

    // Check origin header
    if (origin && ALLOWED_ORIGINS.includes(origin)) {
        return { allowed: true, origin };
    }

    // Check referer as fallback
    if (referer) {
        const refererOrigin = new URL(referer).origin;
        if (ALLOWED_ORIGINS.includes(refererOrigin)) {
            return { allowed: true, origin: refererOrigin };
        }
    }

    return { allowed: false, origin: origin || referer };
}

export async function POST(request) {
    const clientInfo = getClientInfo();

    try {
        // Check origin first
        const originCheck = checkOrigin(request);
        if (!originCheck.allowed) {
            logSecurityEvent('UNAUTHORIZED_ORIGIN', {
                requestedOrigin: originCheck.origin,
                allowedOrigins: ALLOWED_ORIGINS
            });
            return NextResponse.json(
                { error: 'Unauthorized origin' },
                {
                    status: 403,
                    headers: {
                        'Access-Control-Allow-Origin': 'null'
                    }
                }
            );
        }

        const { bankBin, accountNumber } = await request.json();

        // Validate input data
        const validation = isValidBankData(bankBin, accountNumber);
        if (!validation.valid) {
            logSecurityEvent('INVALID_INPUT', {
                error: validation.error,
                bankBin: bankBin?.substring(0, 3) + '***', // Partial log for security
                accountNumber: accountNumber?.substring(0, 3) + '***'
            });
            return NextResponse.json(
                { error: validation.error },
                {
                    status: 400,
                    headers: getSecurityHeaders(originCheck.origin)
                }
            );
        }

        // Detect suspicious activity
        const suspicious = detectSuspiciousActivity(clientInfo, { bankBin, accountNumber });
        if (suspicious.length > 0) {
            logSecurityEvent('SUSPICIOUS_ACTIVITY', {
                flags: suspicious,
                bankBin: bankBin.substring(0, 3) + '***'
            });
        }
        // Create cache key
        const cacheKey = getCacheKey(bankBin, accountNumber);

        // Try to get from cache first (both success and error results)
        const cachedResult = await getCache(cacheKey);
        if (cachedResult) {
           
            return NextResponse.json({
                ...cachedResult,
                fromCache: true,
                rateLimitInfo: {
                    limit: 'unlimited',
                    remaining: 'unlimited',
                    resetTime: null,
                    note: 'No rate limit for cached responses'
                }
            }, {
                headers: getSecurityHeaders(originCheck.origin)
            });
        }

        // Cache miss - apply rate limiting
       
        // Check rate limit (5 requests per minute per IP) for all cache miss requests
        const rateLimitKey = getRateLimitKey(clientInfo.ip, 'api_call');
        const rateLimit = await checkRateLimit(rateLimitKey, 5, 60);

        if (!rateLimit.allowed) {
            logSecurityEvent('RATE_LIMIT_EXCEEDED', {
                ip: clientInfo.ip,
                current: rateLimit.current,
                limit: rateLimit.limit
            });
            return NextResponse.json(
                {
                    error: 'Rate limit exceeded. Maximum 5 API calls per minute. Try again later or wait for cached results.',
                    rateLimitInfo: {
                        limit: rateLimit.limit,
                        remaining: rateLimit.remaining,
                        resetTime: rateLimit.resetTime,
                        note: 'Rate limit applies to all new API calls (cache miss)'
                    }
                },
                {
                    status: 429,
                    headers: {
                        ...getSecurityHeaders(originCheck.origin),
                        'X-RateLimit-Limit': rateLimit.limit.toString(),
                        'X-RateLimit-Remaining': rateLimit.remaining.toString(),
                        'X-RateLimit-Reset': rateLimit.resetTime.toString()
                    }
                }
            );
        }

        // Fetch from API
       

        const result = await getNameAccount(accountNumber, bankBin);
        let responseData;
        let cacheTTL;

        if (result?.creditorInfo?.name) {
            responseData = {
                success: true,
                accountName: result?.creditorInfo?.name,
                bankBin,
                accountNumber
            };
            cacheTTL = 5184000; // Cache success for 1 hour
           
        } else {
            responseData = {
                success: false,
                accountName: "",
                error: "Không tìm thấy tên tài khoản",
                bankBin,
                accountNumber
            };
            cacheTTL = 60; // Cache errors for 30 minutes (shorter than success)
        }

        // Cache both success and error results
        await setCache(cacheKey, responseData, cacheTTL);

        return NextResponse.json({
            ...responseData,
            fromCache: false,
            rateLimitInfo: {
                limit: rateLimit.limit,
                remaining: rateLimit.remaining,
                resetTime: rateLimit.resetTime,
                note: 'Rate limit applies to all new API calls (cache miss)'
            }
        }, {
            headers: getSecurityHeaders(originCheck.origin)
        });

    } catch (error) {
        console.error('Error in bank account lookup:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            {
                status: 500,
                headers: {
                    'Access-Control-Allow-Origin': '*'
                }
            }
        );
    }
}

// Handle CORS preflight requests
export async function OPTIONS(request) {
    const originCheck = checkOrigin(request);

    if (!originCheck.allowed) {
        return new NextResponse(null, {
            status: 403,
            headers: {
                'Access-Control-Allow-Origin': 'null'
            }
        });
    }

    return new NextResponse(null, {
        status: 200,
        headers: {
            'Access-Control-Allow-Origin': originCheck.origin,
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Access-Control-Allow-Credentials': 'true',
            'Access-Control-Max-Age': '86400' // 24 hours
        }
    });
}
