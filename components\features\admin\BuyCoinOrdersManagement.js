'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { RefreshCw, Search, Filter, Eye, Edit, Clock, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import Pagination from '@/components/ui/Pagination';

export default function BuyCoinOrdersManagement() {
    const [orders, setOrders] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [editForm, setEditForm] = useState({
        status: '',
        admin_note: '',
        processed_by: ''
    });
    const [stats, setStats] = useState({
        total: 0,
        created: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        cancelled: 0
    });
    
    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setItemsPerPage] = useState(25);
    const [pagination, setPagination] = useState({
        page: 1,
        limit: 25,
        total: 0,
        totalPages: 0
    });

    // Fetch orders with pagination
    const fetchOrders = async (page = currentPage, limit = itemsPerPage, status = statusFilter, search = searchTerm) => {
        try {
            setLoading(true);
            
            // Build query parameters
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString()
            });
            
            if (status && status !== 'all') {
                params.append('status', status);
            }
            
            if (search && search.trim()) {
                params.append('search', search.trim());
            }
            
            const response = await fetch(`/api/admin/buycoin-orders?${params}`);
            const data = await response.json();

            if (data.success) {
                setOrders(data.orders);
                setStats(data.stats);
                setPagination(data.pagination);
                setCurrentPage(data.pagination.page);
            } else {
                console.error('Failed to fetch orders:', data.error);
            }
        } catch (error) {
            console.error('Error fetching orders:', error);
        } finally {
            setLoading(false);
        }
    };

    // Update order
    const updateOrder = async (orderId, updates) => {
        try {
            const response = await fetch(`/api/admin/buycoin-orders/${orderId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updates),
            });

            const data = await response.json();

            if (data.success) {
                fetchOrders(); // Refresh orders with current pagination
                setIsEditDialogOpen(false);
                setSelectedOrder(null);
            } else {
                console.error('Failed to update order:', data.error);
            }
        } catch (error) {
            console.error('Error updating order:', error);
        }
    };

    // Handle pagination changes
    const handlePageChange = (page) => {
        setCurrentPage(page);
        fetchOrders(page, itemsPerPage, statusFilter, searchTerm);
    };

    const handleItemsPerPageChange = (newItemsPerPage) => {
        setItemsPerPage(newItemsPerPage);
        setCurrentPage(1); // Reset to first page
        fetchOrders(1, newItemsPerPage, statusFilter, searchTerm);
    };

    // Handle search and filter changes
    const handleSearchChange = (value) => {
        setSearchTerm(value);
        setCurrentPage(1); // Reset to first page
        fetchOrders(1, itemsPerPage, statusFilter, value);
    };

    const handleStatusFilterChange = (value) => {
        setStatusFilter(value);
        setCurrentPage(1); // Reset to first page
        fetchOrders(1, itemsPerPage, value, searchTerm);
    };

    // Handle edit order
    const handleEditOrder = (order) => {
        setSelectedOrder(order);
        setEditForm({
            status: order.status,
            admin_note: order.admin_note || '',
            processed_by: order.processed_by || ''
        });
        setIsEditDialogOpen(true);
    };

    // Handle save edit
    const handleSaveEdit = () => {
        if (selectedOrder) {
            updateOrder(selectedOrder.id, editForm);
        }
    };

    // Get status badge variant
    const getStatusBadgeVariant = (status) => {
        switch (status) {
            case 'completed': return 'default';
            case 'processing': return 'secondary';
            case 'failed': return 'destructive';
            case 'cancelled': return 'outline';
            default: return 'secondary';
        }
    };

    // Get status icon
    const getStatusIcon = (status) => {
        switch (status) {
            case 'completed': return <CheckCircle className="w-4 h-4" />;
            case 'processing': return <Clock className="w-4 h-4" />;
            case 'failed': return <XCircle className="w-4 h-4" />;
            case 'cancelled': return <AlertCircle className="w-4 h-4" />;
            default: return <Clock className="w-4 h-4" />;
        }
    };

    // Format currency
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount);
    };

    // Format date
    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleString('vi-VN');
    };

    useEffect(() => {
        fetchOrders();

        // Auto refresh every 30 seconds with current pagination settings
        const interval = setInterval(() => {
            fetchOrders(currentPage, itemsPerPage, statusFilter, searchTerm);
        }, 30000);
        return () => clearInterval(interval);
    }, [currentPage, itemsPerPage, statusFilter, searchTerm]);

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h2 className="text-2xl font-bold">BuyCoin Orders Management</h2>
                    <p className="text-muted-foreground">
                        Quản lý các đơn hàng mua coin từ khách hàng
                    </p>
                </div>
                <Button onClick={fetchOrders} disabled={loading}>
                    <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    Refresh
                </Button>
            </div>

            {/* Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                <Card>
                    <CardContent className="p-4">
                        <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                        <p className="text-sm text-muted-foreground">Total</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="text-2xl font-bold text-gray-600">{stats.created}</div>
                        <p className="text-sm text-muted-foreground">Created</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="text-2xl font-bold text-yellow-600">{stats.processing}</div>
                        <p className="text-sm text-muted-foreground">Processing</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
                        <p className="text-sm text-muted-foreground">Completed</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
                        <p className="text-sm text-muted-foreground">Failed</p>
                    </CardContent>
                </Card>
                <Card>
                    <CardContent className="p-4">
                        <div className="text-2xl font-bold text-orange-600">{stats.cancelled}</div>
                        <p className="text-sm text-muted-foreground">Cancelled</p>
                    </CardContent>
                </Card>
            </div>

            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Filter className="w-5 h-5" />
                        Filters
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="flex flex-col md:flex-row gap-4">
                        <div className="flex-1">
                            <Label htmlFor="search">Search</Label>
                            <div className="relative">
                                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="search"
                                    placeholder="Search by Steam ID, Order ID, or Transaction ID..."
                                    value={searchTerm}
                                    onChange={(e) => handleSearchChange(e.target.value)}
                                    className="pl-10"
                                />
                            </div>
                        </div>
                        <div className="w-full md:w-48">
                            <Label htmlFor="status">Status</Label>
                            <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
                                <SelectTrigger>
                                    <SelectValue placeholder="All statuses" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All statuses</SelectItem>
                                    <SelectItem value="created">Created</SelectItem>
                                    <SelectItem value="processing">Processing</SelectItem>
                                    <SelectItem value="completed">Completed</SelectItem>
                                    <SelectItem value="failed">Failed</SelectItem>
                                    <SelectItem value="cancelled">Cancelled</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Orders Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Orders ({pagination.total})</CardTitle>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <div className="text-center py-8">
                            <RefreshCw className="h-8 w-8 mx-auto mb-2 text-gray-400 animate-spin" />
                            <p className="text-gray-500">Loading orders...</p>
                        </div>
                    ) : orders.length === 0 ? (
                        <div className="text-center py-8">
                            <p className="text-gray-500">No orders found</p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <div className="overflow-x-auto">
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Order ID</TableHead>
                                            <TableHead>Steam ID</TableHead>
                                            <TableHead>Coin Amount</TableHead>
                                            <TableHead>VND Amount</TableHead>
                                            <TableHead>Status</TableHead>
                                            <TableHead>Created</TableHead>
                                            <TableHead>Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {orders.map((order) => (
                                            <TableRow key={order.id}>
                                                <TableCell className="font-mono">{order.id}</TableCell>
                                                <TableCell className="font-mono">{order.steam_id}</TableCell>
                                                <TableCell>{order.coin_amount}</TableCell>
                                                <TableCell>{formatCurrency(order.vnd_amount)}</TableCell>
                                                <TableCell>
                                                    <Badge variant={getStatusBadgeVariant(order.status)} className="flex items-center gap-1 w-fit">
                                                        {getStatusIcon(order.status)}
                                                        {order.status}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>{formatDate(order.created_at)}</TableCell>
                                                <TableCell>
                                                    <div className="flex gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            onClick={() => handleEditOrder(order)}
                                                        >
                                                            <Edit className="w-4 h-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>

                                {/* Pagination */}
                                <Pagination
                                    currentPage={pagination.page}
                                    totalPages={pagination.totalPages}
                                    totalItems={pagination.total}
                                    itemsPerPage={itemsPerPage}
                                    onPageChange={handlePageChange}
                                    onItemsPerPageChange={handleItemsPerPageChange}
                                      showAlways={true}
                                    className="mt-4"
                                />
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Edit Order Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Edit Order</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                        <div>
                            <Label htmlFor="edit-status">Status</Label>
                            <Select value={editForm.status} onValueChange={(value) => setEditForm({...editForm, status: value})}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="created">Created</SelectItem>
                                    <SelectItem value="processing">Processing</SelectItem>
                                    <SelectItem value="completed">Completed</SelectItem>
                                    <SelectItem value="failed">Failed</SelectItem>
                                    <SelectItem value="cancelled">Cancelled</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Label htmlFor="edit-processed-by">Processed By</Label>
                            <Input
                                id="edit-processed-by"
                                value={editForm.processed_by}
                                onChange={(e) => setEditForm({...editForm, processed_by: e.target.value})}
                                placeholder="Admin username"
                            />
                        </div>
                        <div>
                            <Label htmlFor="edit-admin-note">Admin Note</Label>
                            <Textarea
                                id="edit-admin-note"
                                value={editForm.admin_note}
                                onChange={(e) => setEditForm({...editForm, admin_note: e.target.value})}
                                placeholder="Add admin notes..."
                                rows={3}
                            />
                        </div>
                        <div className="flex justify-end gap-2">
                            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                                Cancel
                            </Button>
                            <Button onClick={handleSaveEdit}>
                                Save Changes
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    );
}
