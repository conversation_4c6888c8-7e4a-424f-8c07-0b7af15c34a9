"use client"

import { order } from "@/actions/order"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useFormState } from "react-dom"
import { Coins } from "lucide-react"
import { orderSchema } from "@/validations/order.schema"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

// Import custom components
import SteamIdInput from "./SteamIdInput"
import CurrencyExchange from "./CurrencyExchange"
import BankSelector from "./BankSelector"
import SubmitButton from "./SubmitButton"
import FormMessages from "./FormMessages"
import OrderModal from "./OrderModal"

// Import custom hooks
import { useCurrencyExchange } from "./hooks/useCurrencyExchange"
import { useOrderModal } from "./hooks/useOrderModal"
import { useSharedSteamIds } from "@/hooks/useSharedSteamIds"

export default function SellCoinForm({ banks, rate, availableCoins, steamId }) {
  const defaultBankNo = banks.length > 0 ? banks[0].bankNo : ""
  const [state, formAction] = useFormState(order, null)
  const {
    register,
    formState: { errors },
    reset
  } = useForm({
    resolver: zodResolver(orderSchema),
  })

  // Custom hooks
  const { coin, vnd, handleCoinChange, handleVndChange, resetValues } = useCurrencyExchange(rate)
  const { isModalOpen, handleModalClose, handleOrderSuccess } = useOrderModal()

  // Shared Steam IDs hook
  const {
    isLoaded,
    steamIds,
    addSteamId,
    removeSteamId,
    clearAllData
  } = useSharedSteamIds()

  // Bank selection state
  const [selectedBank, setSelectedBank] = useState(defaultBankNo)

  // Handle modal close with proper cleanup
  const onModalClose = () => {
    handleModalClose(formAction, reset, resetValues)
  }

  // Handle order success
  useEffect(() => {
    handleOrderSuccess(state)
  }, [state, handleOrderSuccess])

  return (
    <>
      <Card className="border-0 shadow-xl bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm">
        <CardHeader className="pb-3 md:pb-4 p-4 md:p-6">
          <CardTitle className="flex items-center space-x-2 md:space-x-3 text-xl md:text-2xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            <Coins className="h-6 w-6 md:h-7 md:w-7 text-blue-600" />
            <span>Đặt Hàng Coin</span>
          </CardTitle>
          <p className="text-sm md:text-base text-gray-600 dark:text-gray-400">
            Nhập thông tin để mua coin với tỷ giá tốt nhất
          </p>
        </CardHeader>

        <CardContent className="p-4 md:p-6 pt-0">
          <form action={formAction} className="space-y-4 md:space-y-6">
            {/* Steam ID Input Component */}
            <SteamIdInput
              steamId={steamId}
              steamIds={steamIds}
              onAddSteamId={addSteamId}
              onRemoveSteamId={removeSteamId}
              register={register}
              errors={errors}
              state={state}
            />

            {/* Currency Exchange Component */}
            <CurrencyExchange
              coin={coin}
              vnd={vnd}
              rate={rate}
              onCoinChange={handleCoinChange}
              onVndChange={handleVndChange}
              register={register}
              errors={errors}
              state={state}
            />

            {/* Bank Selection Component */}
            <BankSelector
              banks={banks}
              selectedBank={selectedBank}
              onBankChange={setSelectedBank}
              register={register}
              errors={errors}
              state={state}
            />

            {/* Form Messages Component */}
            <FormMessages state={state} />

            {/* Submit Button */}
            <div className="pt-3 md:pt-4">
              <SubmitButton />
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Order Modal Component */}
      <OrderModal
        isOpen={isModalOpen}
        onClose={onModalClose}
        state={state}
      />
    </>
  )
}
