'use client'
import ItemBox from '@/components/features/etopfun/ItemBox'
export function ItemGrid({ items, selectedItems, onItemSelect }) {
  const availableItems = items.filter(item => !selectedItems.some(selectedItem => selectedItem.id === item.id))
  return (
    <div className=" dark:border dark:shadow-none shadow-slate-200-100 shadow-md p-3 md:p-6 rounded-lg">
      <div className="flex items-center gap-2 mb-4">
        <h2 className="text-lg font-semibold">Hòm đồ</h2>
      </div>
      <div className='flex flex-row flex-wrap content-start gap-2 overflow-auto custom-scrollbar h-[300px] md:h-[540px]'>
        {availableItems.map((item) => (
          <div key={item.id} onClick={() => onItemSelect(item)} className="cursor-pointer" >
            <ItemBox
              imageUrl={item.image}
              imageBottomShow={item.imageBottomShow}
              price={item.value}
              status={item.status}
            />
          </div>
        ))}
      </div>
    </div>
  )
}