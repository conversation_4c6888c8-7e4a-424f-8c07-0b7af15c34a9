"use client";
import { motion } from "framer-motion";
import { Loader2, <PERSON><PERSON>, Credit<PERSON>ard, CheckCircle2 } from "lucide-react";

export function LoadingSpinner({ size = "default", className = "" }) {
  const sizeClasses = {
    sm: "h-4 w-4",
    default: "h-6 w-6",
    lg: "h-8 w-8",
    xl: "h-12 w-12"
  };

  return (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      className={`${sizeClasses[size]} ${className}`}
    >
      <Loader2 className="h-full w-full" />
    </motion.div>
  );
}

export function PaymentLoadingState() {
  const steps = [
    { icon: Coins, label: "Xử lý đơn hàng", delay: 0 },
    { icon: CreditCard, label: "Tạo thông tin thanh toán", delay: 0.5 },
    { icon: CheckCircle2, label: "Hoàn tất", delay: 1 }
  ];

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] space-y-8">
      <div className="relative">
        <motion.div
          className="w-20 h-20 border-4 border-blue-200 dark:border-blue-800 rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        />
        <motion.div
          className="absolute inset-2 w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full"
          animate={{ rotate: -360 }}
          transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
        />
        <div className="absolute inset-0 flex items-center justify-center">
          <Coins className="h-8 w-8 text-blue-600" />
        </div>
      </div>

      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
          Đang xử lý thanh toán
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Vui lòng đợi trong giây lát...
        </p>
      </div>

      <div className="flex space-x-8">
        {steps.map((step, index) => {
          const IconComponent = step.icon;
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0.3, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: step.delay, duration: 0.5 }}
              className="flex flex-col items-center space-y-2"
            >
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                <IconComponent className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <span className="text-xs text-gray-600 dark:text-gray-400 text-center max-w-[80px]">
                {step.label}
              </span>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
}

export function SkeletonCard({ className = "" }) {
  return (
    <div className={`animate-pulse ${className}`}>
      <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-4 w-3/4 mb-2"></div>
      <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-4 w-1/2 mb-2"></div>
      <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-4 w-5/6"></div>
    </div>
  );
}

export function PulsingDot({ color = "blue", size = "default" }) {
  const colorClasses = {
    blue: "bg-blue-500",
    green: "bg-green-500",
    red: "bg-red-500",
    yellow: "bg-yellow-500",
    purple: "bg-purple-500"
  };

  const sizeClasses = {
    sm: "w-2 h-2",
    default: "w-3 h-3",
    lg: "w-4 h-4"
  };

  return (
    <motion.div
      className={`${colorClasses[color]} ${sizeClasses[size]} rounded-full`}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [1, 0.7, 1]
      }}
      transition={{
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  );
}

export function ProgressBar({ progress = 0, className = "" }) {
  return (
    <div className={`w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 ${className}`}>
      <motion.div
        className="bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full"
        initial={{ width: 0 }}
        animate={{ width: `${progress}%` }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      />
    </div>
  );
}
